<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32 Monitor - <PERSON><PERSON><PERSON><PERSON>ị</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mqtt/4.3.7/mqtt.min.js"></script>
    <style>
      .device-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 20px;
      }

      .device-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }

      .device-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
      }

      .device-body {
        padding: 20px;
      }

      .device-footer {
        padding: 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
      }

      .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
      }

      .status-online {
        background-color: #d4edda;
        color: #155724;
      }

      .status-offline {
        background-color: #f8d7da;
        color: #721c24;
      }

      .device-icon {
        font-size: 2.5rem;
        color: #4e73df;
        margin-bottom: 15px;
      }

      .device-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
      }

      .stat-item {
        text-align: center;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
        flex: 1;
        margin: 0 5px;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 700;
        color: #4e73df;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
      }

      .add-device-card {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .add-device-card:hover {
        border-color: #4e73df;
        background-color: #f8f9fa;
      }

      .add-icon {
        font-size: 3rem;
        color: #4e73df;
        margin-bottom: 15px;
      }
    </style>
  </head>
  <body>
    <!-- Navbar -->
    <nav class="navbar">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/home" class="nav-link">
            <i class="fas fa-tachometer-alt"></i> Bảng Điều Khiển
          </a>
        </li>
        <li class="nav-item">
          <a href="/devices" class="nav-link active">
            <i class="fas fa-microchip"></i> Thiết Bị
          </a>
        </li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link">
            <i class="fas fa-fire"></i> Ảnh Tầm Nhiệt
          </a>
        </li>

      </ul>

      <div class="navbar-right">
        <button class="btn btn-danger" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i> Đăng Xuất
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <div id="mainContent" style="display: none">
      <!-- Welcome Header -->
      <div class="card mb-4">
        <div
          class="card-body d-flex justify-content-between align-items-center"
        >
          <div>
            <h1 class="mb-0">
              Quản Lý Thiết Bị
            </h1>
            <p class="text-muted">
              Xin chào, <span id="currentUser" class="text-primary"></span>! Quản lý các thiết bị ESP32 của bạn tại đây.
            </p>
          </div>
          <div>
            <span class="badge bg-success p-2 mb-2 d-block">
              <i class="fas fa-circle"></i> Hệ Thống Hoạt Động
            </span>
            <span class="text-muted small">
              Cập nhật lần cuối: <span id="lastUpdate">Vừa xong</span>
            </span>
          </div>
        </div>
      </div>

      <!-- Device Registration -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Đăng Ký Thiết Bị Mới</h5>
          <button class="btn btn-sm btn-outline-primary">
            <i class="fas fa-question-circle"></i> Trợ Giúp
          </button>
        </div>
        <div class="card-body">
          <div class="form-group mb-3">
            <label for="esp32Id">ID Thiết Bị ESP32</label>
            <div class="input-group">
              <input
                type="text"
                id="esp32Id"
                class="form-control"
                placeholder="Nhập ID thiết bị"
              />
              <button class="btn btn-primary" onclick="registerDevice()">
                <i class="fas fa-plus-circle"></i> Thêm Thiết Bị
              </button>
            </div>
            <small class="text-muted">Nhập định danh duy nhất của thiết bị ESP32 của bạn</small>
          </div>
        </div>
      </div>

      <!-- Devices List -->
      <h5 class="mb-3">Thiết Bị Của Bạn</h5>
      <div class="row" id="devicesList">
        <!-- Devices will be added here dynamically -->

        <!-- Add Device Card -->
        <div class="col-md-4">
          <div class="add-device-card" onclick="focusDeviceInput()">
            <div class="add-icon">
              <i class="fas fa-plus-circle"></i>
            </div>
            <h4>Thêm Thiết Bị Mới</h4>
            <p class="text-muted">Nhấp vào đây để thêm thiết bị ESP32 mới</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="row">
          <div class="col-4">
            <h3>ESP32 Monitor</h3>
            <p>Giải pháp giám sát môi trường tiên tiến cho nhà ở và doanh nghiệp.</p>
          </div>
          <div class="col-4">
            <h3>Liên Kết Nhanh</h3>
            <ul>
              <li><a href="/home">Bảng Điều Khiển</a></li>
              <li><a href="/devices">Thiết Bị</a></li>
              <li><a href="/mlx90640">Ảnh Tầm Nhiệt</a></li>
            </ul>
          </div>
          <div class="col-4">
            <h3>Liên Hệ</h3>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +84 123 456 789</p>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-linkedin"></i></a>
            </div>
          </div>
        </div>
        <div class="text-center mt-4">
          <p>&copy; 2023 ESP32 Monitor. Đã đăng ký bản quyền.</p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script>
      // Biến toàn cục
      let isAdmin = false;
      let currentUser = "";
      let currentDevice = "";
      let devicesList = [];

      // Cập nhật thời gian
      function updateTimestamp() {
        document.getElementById("lastUpdate").textContent =
          new Date().toLocaleTimeString();
      }

      // Đăng ký thiết bị
      function registerDevice() {
        const deviceId = document.getElementById("esp32Id").value;

        if (!deviceId) {
          alert("Vui lòng nhập ID thiết bị");
          return;
        }

        fetch("/set_device", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ device_id: deviceId }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              alert("Đăng ký thiết bị thành công!");
              loadDevices();
            } else {
              alert(data.message || "Không thể đăng ký thiết bị");
            }
          })
          .catch((error) => {
            console.error("Lỗi:", error);
            alert("Đăng ký thiết bị thất bại. Vui lòng thử lại.");
          });
      }

      // Tải danh sách thiết bị
      function loadDevices() {
        fetch("/get_devices", {
          credentials: "include"
        })
        .then(response => response.json())
        .then(data => {
          if (data.status === "success") {
            devicesList = data.devices;
            renderDevices();
          } else {
            console.error("Lỗi khi tải thiết bị:", data.message);
          }
        })
        .catch(error => {
          console.error("Lỗi khi tải thiết bị:", error);
        });
      }

      // Hiển thị danh sách thiết bị
      function renderDevices() {
        const devicesContainer = document.getElementById("devicesList");

        // Xóa tất cả thiết bị hiện tại trừ thẻ "Thêm thiết bị mới"
        const addDeviceCard = devicesContainer.lastElementChild;
        devicesContainer.innerHTML = '';

        // Thêm các thiết bị
        devicesList.forEach(device => {
          const deviceCard = createDeviceCard(device);
          devicesContainer.appendChild(deviceCard);
        });

        // Thêm lại thẻ "Thêm thiết bị mới"
        devicesContainer.appendChild(addDeviceCard);
      }

      // Tạo thẻ thiết bị
      function createDeviceCard(device) {
        const col = document.createElement('div');
        col.className = 'col-md-4';

        const isOnline = device.last_seen ? (new Date() - new Date(device.last_seen * 1000) < 5 * 60 * 1000) : false;

        col.innerHTML = `
          <div class="device-card">
            <div class="device-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">${device.device_id}</h5>
              <span class="status-badge ${isOnline ? 'status-online' : 'status-offline'}">
                ${isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            <div class="device-body text-center">
              <div class="device-icon">
                <i class="fas fa-microchip"></i>
              </div>
              <h4>${device.name || 'ESP32 Device'}</h4>
              <p class="text-muted">${device.description || 'Thiết bị giám sát môi trường'}</p>

              <div class="device-stats">
                <div class="stat-item">
                  <div class="stat-value">${device.temperature || 'N/A'}</div>
                  <div class="stat-label">Nhiệt độ</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">${device.humidity || 'N/A'}</div>
                  <div class="stat-label">Độ ẩm</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">${device.last_seen ? new Date(device.last_seen * 1000).toLocaleTimeString() : 'N/A'}</div>
                  <div class="stat-label">Lần cuối</div>
                </div>
              </div>
            </div>
            <div class="device-footer d-flex justify-content-between">
              <button class="btn btn-sm btn-primary" onclick="viewDevice('${device.device_id}')">
                <i class="fas fa-eye"></i> Xem
              </button>
              <button class="btn btn-sm btn-danger" onclick="deleteDevice('${device.device_id}')">
                <i class="fas fa-trash"></i> Xóa
              </button>
            </div>
          </div>
        `;

        return col;
      }

      // Xem thiết bị
      function viewDevice(deviceId) {
        window.location.href = `/home?device=${deviceId}`;
      }

      // Xóa thiết bị
      function deleteDevice(deviceId) {
        if (confirm(`Bạn có chắc chắn muốn xóa thiết bị ${deviceId}?`)) {
          fetch("/delete_device", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            credentials: "include",
            body: JSON.stringify({ device_id: deviceId }),
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.status === "success") {
                alert("Xóa thiết bị thành công!");
                loadDevices();
              } else {
                alert(data.message || "Không thể xóa thiết bị");
              }
            })
            .catch((error) => {
              console.error("Lỗi:", error);
              alert("Xóa thiết bị thất bại. Vui lòng thử lại.");
            });
        }
      }

      // Focus vào ô nhập ID thiết bị
      function focusDeviceInput() {
        document.getElementById("esp32Id").focus();
      }

      // Đăng xuất
      function logout() {
        fetch("/logout", {
          method: "POST",
          credentials: "include",
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              window.location.href = "/";
            } else {
              alert("Đăng xuất thất bại: " + data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi:", error);
            alert("Đăng xuất thất bại. Vui lòng thử lại.");
          });
      }

      // Xử lý sự kiện khi trang tải xong
      window.onload = function() {
        // Ẩn nội dung chính ban đầu
        document.getElementById("mainContent").style.display = "none";

        // Kiểm tra phiên đăng nhập
        fetch("/check_session", {
          credentials: "include",
        })
          .then((response) => {
            if (response.ok) {
              return response.json();
            } else {
              console.log("Chưa đăng nhập, chuyển hướng đến trang đăng nhập");
              // Chuyển hướng đến trang đăng nhập sau 1 giây
              setTimeout(function() {
                window.location.href = "/login";
              }, 1000);
              return null;
            }
          })
          .then((data) => {
            if (data && data.status === "success") {
              console.log("Đã đăng nhập, hiển thị nội dung");
              // Hiển thị nội dung chính
              document.getElementById("mainContent").style.display = "block";

              document.getElementById("currentUser").textContent = data.username;
              currentUser = data.username;
              isAdmin = data.isAdmin;

              // Tải danh sách thiết bị
              loadDevices();

              // Cập nhật thời gian
              updateTimestamp();
              setInterval(updateTimestamp, 60000); // Cập nhật mỗi phút
            } else if (data === null) {
              // Đã xử lý trong phần response.ok == false
            } else {
              console.log("Lỗi phiên, chuyển hướng đến trang đăng nhập");
              // Chuyển hướng đến trang đăng nhập sau 1 giây
              setTimeout(function() {
                window.location.href = "/login";
              }, 1000);
            }
          })
          .catch((error) => {
            console.error("Lỗi kiểm tra phiên:", error);
            // Chuyển hướng đến trang đăng nhập sau 1 giây
            setTimeout(function() {
              window.location.href = "/login";
            }, 1000);
          });
      };
    </script>
  </body>
</html>
