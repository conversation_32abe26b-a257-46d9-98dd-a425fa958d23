/* Global Styles */
:root {
  --primary-color: #4e73df;
  --secondary-color: #1cc88a;
  --dark-color: #5a5c69;
  --light-color: #f8f9fc;
  --danger-color: #e74a3b;
  --warning-color: #f6c23e;
  --card-border-radius: 0.35rem;
  --transition-speed: 0.3s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Nunito', 'Segoe UI', Roboto, Arial, sans-serif;
  background-color: var(--light-color);
  color: var(--dark-color);
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
}

a:hover {
  color: #2e59d9;
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-block;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: all var(--transition-speed) ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2653d4;
}

.btn-success {
  color: #fff;
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success:hover {
  background-color: #17a673;
  border-color: #169b6b;
}

.btn-danger {
  color: #fff;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: #e02d1b;
  border-color: #d52a1a;
}

/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.navbar-brand img {
  height: 40px;
  margin-right: 0.5rem;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 0.5rem;
}

.nav-link {
  color: var(--dark-color);
  padding: 0.5rem 1rem;
  font-weight: 600;
  border-radius: 0.25rem;
  transition: all var(--transition-speed) ease;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: rgba(78, 115, 223, 0.1);
  text-decoration: none;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* Cards */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid #e3e6f0;
  border-radius: var(--card-border-radius);
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  margin-bottom: 1.5rem;
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
  border-top-left-radius: var(--card-border-radius);
  border-top-right-radius: var(--card-border-radius);
}

.card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

/* Forms */
.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #6e707e;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
  transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.form-control:focus {
  color: #6e707e;
  background-color: #fff;
  border-color: #bac8f3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-group {
  margin-bottom: 1rem;
}

/* Tables */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--dark-color);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #e3e6f0;
  text-align: left;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #e3e6f0;
  background-color: #f8f9fc;
  color: var(--dark-color);
  font-weight: 700;
}

.table tbody tr:hover {
  background-color: rgba(78, 115, 223, 0.05);
}

.table-bordered {
  border: 1px solid #e3e6f0;
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #e3e6f0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, rgba(78, 115, 223, 0.9), rgba(26, 32, 44, 0.9)), url('/static/images/background.jpg');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 6rem 0;
  text-align: center;
}

.hero-section h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: white;
}

.hero-section p {
  font-size: 1.25rem;
  max-width: 800px;
  margin: 0 auto 2rem auto;
}

/* Features */
.feature-card {
  text-align: center;
  padding: 2rem;
  height: 100%;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

/* Footer */
.footer {
  background-color: #2c3e50;
  color: white;
  padding: 3rem 0;
  margin-top: 3rem;
}

.footer a {
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-speed) ease;
}

.footer a:hover {
  color: white;
  text-decoration: none;
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-success {
  color: var(--secondary-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-success {
  background-color: var(--secondary-color) !important;
}

.bg-danger {
  background-color: var(--danger-color) !important;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* Responsive */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 0.5rem;
  }

  .navbar-brand {
    margin-bottom: 0.5rem;
  }

  .navbar-nav {
    flex-direction: column;
    width: 100%;
  }

  .nav-item {
    margin: 0.25rem 0;
    width: 100%;
    text-align: center;
  }

  .navbar-right {
    margin-top: 0.5rem;
    width: 100%;
    justify-content: center;
  }

  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section p {
    font-size: 1rem;
  }
}