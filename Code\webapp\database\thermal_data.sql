-- T<PERSON><PERSON> bảng lưu dữ liệu nhiệt MLX90640
CREATE TABLE IF NOT EXISTS `thermal_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL DEFAULT 'default',
  `timestamp` bigint NOT NULL,
  `thermal_matrix` JSON NOT NULL,
  `min_temp` float NOT NULL,
  `max_temp` float NOT NULL,
  `avg_temp` float NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_timestamp` (`device_id`, `timestamp`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Tạo index để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS `idx_device_created` ON `thermal_data` (`device_id`, `created_at`);

-- Thêm comment cho bảng
ALTER TABLE `thermal_data` COMMENT = '<PERSON><PERSON>u trữ dữ liệu nhiệt từ cảm biến MLX90640';
