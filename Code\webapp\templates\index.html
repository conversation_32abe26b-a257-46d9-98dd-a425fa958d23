<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32 Monitor - Bảng <PERSON></title>
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mqtt/4.3.7/mqtt.min.js"></script>
  </head>
  <body>
    <!-- Navbar -->
    <nav class="navbar">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/home" class="nav-link active">
            <i class="fas fa-tachometer-alt"></i> Bảng Điều Khiển
          </a>
        </li>
        <li class="nav-item">
          <a href="/devices" class="nav-link">
            <i class="fas fa-microchip"></i> Thiết Bị
          </a>
        </li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link">
            <i class="fas fa-fire"></i> Ảnh Tầm Nhiệt
          </a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">
            <i class="fas fa-chart-line"></i> Phân Tích
          </a>
        </li>
      </ul>

      <div class="navbar-right">
        <button class="btn btn-danger" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i> Đăng Xuất
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <div id="mainContent" style="display: none">
      <!-- Welcome Header -->
      <div class="card mb-4">
        <div
          class="card-body d-flex justify-content-between align-items-center"
        >
          <div>
            <h1 class="mb-0">
              Xin chào, <span id="currentUser" class="text-primary"></span>!
            </h1>
            <p class="text-muted">
              Đây là tổng quan về hệ thống giám sát của bạn
            </p>
          </div>
          <div>
            <span class="badge bg-success p-2 mb-2 d-block">
              <i class="fas fa-circle"></i> Hệ Thống Hoạt Động
            </span>
            <span class="text-muted small">
              Cập nhật lần cuối: <span id="lastUpdate">Vừa xong</span>
            </span>
          </div>
        </div>
      </div>

      <!-- Device Info -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Thiết Bị Đang Kết Nối</h5>
              <a href="/devices" class="btn btn-sm btn-primary">
                <i class="fas fa-cog"></i> Quản Lý Thiết Bị
              </a>
            </div>
            <div class="card-body">
              <p class="text-muted mb-3">Thông tin về thiết bị ESP32 đang kết nối và trạng thái hiện tại.</p>
              <div class="table-responsive">
                <table class="table table-hover" aria-describedby="deviceTableDesc">
                  <caption id="deviceTableDesc">Danh sách thiết bị ESP32 đang kết nối và trạng thái hoạt động hiện tại</caption>
                  <thead>
                    <tr>
                      <th>ID Thiết Bị</th>
                      <th>Trạng Thái</th>
                      <th>Lần Cuối Hoạt Động</th>
                      <th>Thao Tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><span id="currentDevice">-</span></td>
                      <td><span class="badge bg-success">Online</span></td>
                      <td>Vừa xong</td>
                      <td>
                        <button class="btn btn-sm btn-primary" onclick="refreshDeviceData()">
                          <i class="fas fa-sync"></i> Làm Mới
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Tables -->
      <div class="row">
        <div class="col-12">
          <!-- Current Data -->
          <div id="deviceTable" class="card mb-4" style="display: none">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Dữ Liệu Thiết Bị Hiện Tại</h5>
              <div>
                <span class="badge bg-primary">Tự động làm mới: BẬT</span>
                <button class="btn btn-sm btn-outline-secondary ms-2">
                  <i class="fas fa-download"></i> Xuất
                </button>
              </div>
            </div>
            <div class="card-body">
              <p class="text-muted mb-3">Dữ liệu cảm biến thời gian thực từ thiết bị ESP32 đang kết nối hiển thị điều kiện môi trường hiện tại.</p>
              <div class="table-responsive">
                <table class="table table-bordered" aria-describedby="currentDataTableDesc">
                  <caption id="currentDataTableDesc">Dữ liệu môi trường thời gian thực từ thiết bị ESP32 đang kết nối bao gồm PM2.5, PM10, độ ẩm và nhiệt độ</caption>
                  <thead>
                    <tr>
                      <th>ID Thiết Bị</th>
                      <th>PM2.5 (µg/m³)</th>
                      <th>PM10 (µg/m³)</th>
                      <th>Độ Ẩm (%)</th>
                      <th>Nhiệt Độ (°C)</th>
                    </tr>
                  </thead>
                  <tbody id="deviceData"></tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Historical Data -->
          <div id="sensorTable" class="card mb-4" style="display: none">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Lịch Sử Đọc Cảm Biến</h5>
              <div>
                <select
                  class="form-control form-control-sm d-inline-block"
                  style="width: auto"
                >
                  <option>24 giờ qua</option>
                  <option>7 ngày qua</option>
                  <option>30 ngày qua</option>
                  <option>Tùy chỉnh</option>
                </select>
                <button class="btn btn-sm btn-outline-secondary ms-2">
                  <i class="fas fa-download"></i> Xuất
                </button>
              </div>
            </div>
            <div class="card-body">
              <p class="text-muted mb-3">Lịch sử ghi nhận từ cảm biến hiển thị xu hướng dữ liệu môi trường theo thời gian.</p>
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <caption>Lịch sử đọc cảm biến gần đây</caption>
                  <thead>
                    <tr>
                      <th>Thời Gian</th>
                      <th>Nhiệt Độ (°C)</th>
                      <th>Độ Ẩm (%)</th>
                      <th>PM2.5 (µg/m³)</th>
                      <th>PM10 (µg/m³)</th>
                    </tr>
                  </thead>
                  <tbody id="sensorData"></tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- MQTT Data History -->
          <div id="mqttHistoryTable" class="card mb-4" style="display: none">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Lịch Sử Dữ Liệu MQTT (32 Lần Truyền)</h5>
              <div>
                <button class="btn btn-sm btn-primary" onclick="loadMqttHistory()">
                  <i class="fas fa-sync"></i> Làm Mới Dữ Liệu
                </button>
                <button class="btn btn-sm btn-outline-secondary ms-2">
                  <i class="fas fa-download"></i> Xuất
                </button>
              </div>
            </div>
            <div class="card-body">
              <p class="text-muted mb-3">Lịch sử đầy đủ của các lần truyền MQTT với tối đa 32 bản ghi, mỗi bản ghi có 24 phần tử.</p>
              <div class="alert alert-info" id="mqttHistoryInfo">
                <i class="fas fa-info-circle"></i> Nhấp "Làm Mới Dữ Liệu" để tải lịch sử truyền MQTT.
              </div>
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <caption>Lịch sử truyền MQTT (tối đa 32 bản ghi)</caption>
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>Thời Gian</th>
                      <th>Số Phần Tử</th>
                      <th>Thao Tác</th>
                    </tr>
                  </thead>
                  <tbody id="mqttHistoryData"></tbody>
                </table>
              </div>

              <!-- Transmission Details Modal -->
              <div class="modal" id="transmissionDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">Chi Tiết Lần Truyền</h5>
                      <button type="button" class="btn-close" onclick="closeTransmissionModal()"></button>
                    </div>
                    <div class="modal-body">
                      <div id="transmissionDetails"></div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" onclick="closeTransmissionModal()">Đóng</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Admin Panel -->
          <div id="adminPanel" class="card mb-4" style="display: none">
            <div
              class="card-header bg-danger text-white d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">
                <i class="fas fa-shield-alt"></i> Bảng Điều Khiển Quản Trị
              </h5>
              <span class="badge bg-warning text-dark">Quyền Quản Trị</span>
            </div>
            <div class="card-body">
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Cảnh báo: Các thao tác
                thực hiện ở đây ảnh hưởng đến tất cả người dùng và thiết bị.
              </div>
              <p class="text-muted mb-3">Tổng quan quản trị về tất cả thiết bị đã đăng ký và chủ sở hữu của chúng với các điều khiển quản lý.</p>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <caption>Danh sách thiết bị đã đăng ký</caption>
                  <thead>
                    <tr>
                      <th>ID Thiết Bị</th>
                      <th>Chủ Sở Hữu</th>
                      <th>Trạng Thái</th>
                      <th>Thao Tác</th>
                    </tr>
                  </thead>
                  <tbody id="adminDeviceList"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="row">
          <div class="col-4">
            <h3>ESP32 Monitor</h3>
            <p>Giải pháp giám sát môi trường tiên tiến cho nhà ở và doanh nghiệp.</p>
          </div>
          <div class="col-4">
            <h3>Liên Kết Nhanh</h3>
            <ul>
              <li><a href="/home">Bảng Điều Khiển</a></li>
              <li><a href="/devices">Thiết Bị</a></li>
              <li><a href="/mlx90640">Ảnh Tầm Nhiệt</a></li>
              <li><a href="#">Phân Tích</a></li>
            </ul>
          </div>
          <div class="col-4">
            <h3>Liên Hệ</h3>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +84 123 456 789</p>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-linkedin"></i></a>
            </div>
          </div>
        </div>
        <div class="text-center mt-4">
          <p>&copy; 2023 ESP32 Monitor. Đã đăng ký bản quyền.</p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script>
      // Biến toàn cục
      let isAdmin = false;
      let currentUser = "";
      let currentDevice = "";
      const client = mqtt.connect("ws://broker.emqx.io:1883/mqtt");

      // Cập nhật thời gian
      function updateTimestamp() {
        document.getElementById("lastUpdate").textContent =
          new Date().toLocaleTimeString();
      }

      // Làm mới dữ liệu thiết bị
      function refreshDeviceData() {
        if (!currentDevice) {
          alert("Không có thiết bị nào được kết nối");
          return;
        }

        updateTimestamp();
        loadMqttHistory();
        alert("Đã làm mới dữ liệu thiết bị");
      }

      // Đăng ký nhận dữ liệu thiết bị
      function subscribeToDevice(deviceId) {
        client.subscribe(`esp32/${deviceId}/data`);
        document.getElementById("sensorTable").style.display = "block";
        document.getElementById("deviceTable").style.display = "block";
        document.getElementById("mqttHistoryTable").style.display = "block";
        updateTimestamp();
      }

      // Tải dữ liệu lịch sử MQTT
      function loadMqttHistory() {
        if (!currentDevice) {
          alert("Vui lòng đăng ký thiết bị trước");
          return;
        }

        document.getElementById("mqttHistoryInfo").innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu lịch sử MQTT...';

        fetch(`/get_mqtt_history?device_id=${currentDevice}`, {
          credentials: "include"
        })
        .then(response => response.json())
        .then(data => {
          if (data.status === "success") {
            displayMqttHistory(data.history);
            document.getElementById("mqttHistoryInfo").innerHTML =
              `<i class="fas fa-info-circle"></i> Hiển thị ${data.history_count} trong số 32 lần truyền có thể có.`;
          } else {
            document.getElementById("mqttHistoryInfo").innerHTML =
              `<i class="fas fa-exclamation-triangle"></i> Lỗi: ${data.message}`;
          }
        })
        .catch(error => {
          console.error("Lỗi khi tải lịch sử MQTT:", error);
          document.getElementById("mqttHistoryInfo").innerHTML =
            '<i class="fas fa-exclamation-triangle"></i> Không thể tải lịch sử MQTT. Vui lòng thử lại.';
        });
      }

      // Hiển thị dữ liệu lịch sử MQTT
      function displayMqttHistory(history) {
        const tableBody = document.getElementById("mqttHistoryData");
        tableBody.innerHTML = "";

        if (!history || history.length === 0) {
          tableBody.innerHTML = `
            <tr>
              <td colspan="4" class="text-center">Không có dữ liệu lịch sử</td>
            </tr>
          `;
          return;
        }

        history.forEach((transmission, index) => {
          const timestamp = new Date().toLocaleString(); // Trong ứng dụng thực tế, điều này sẽ đến từ dữ liệu truyền
          const elementCount = Object.keys(transmission).length;

          const row = document.createElement("tr");
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>${timestamp}</td>
            <td>${elementCount} phần tử</td>
            <td>
              <button class="btn btn-sm btn-primary" onclick="showTransmissionDetails(${index})">
                <i class="fas fa-eye"></i> Xem
              </button>
            </td>
          `;
          tableBody.appendChild(row);
        });

        // Lưu dữ liệu lịch sử trong biến toàn cục để truy cập bởi chế độ xem chi tiết
        window.mqttHistoryData = history;
      }

      // Hiển thị chi tiết lần truyền
      function showTransmissionDetails(index) {
        const transmission = window.mqttHistoryData[index];
        const detailsContainer = document.getElementById("transmissionDetails");

        let detailsHTML = `<h6>Lần Truyền #${index + 1}</h6>`;
        detailsHTML += `<div class="table-responsive"><table class="table table-bordered">`;
        detailsHTML += `<thead><tr><th>Tham Số</th><th>Giá Trị</th></tr></thead><tbody>`;

        for (const [key, value] of Object.entries(transmission)) {
          detailsHTML += `
            <tr>
              <td><strong>${key}</strong></td>
              <td>${value}</td>
            </tr>
          `;
        }

        detailsHTML += `</tbody></table></div>`;
        detailsContainer.innerHTML = detailsHTML;

        // Hiển thị modal
        document.getElementById("transmissionDetailsModal").style.display = "block";
      }

      // Đóng modal chi tiết lần truyền
      function closeTransmissionModal() {
        document.getElementById("transmissionDetailsModal").style.display = "none";
      }

      // Xử lý tin nhắn MQTT
      client.on("message", (topic, message) => {
        try {
          const data = JSON.parse(message.toString());
          let tableBody = document.getElementById("deviceData");
          let sensorBody = document.getElementById("sensorData");
          let currentTime = new Date().toLocaleTimeString();

          // Cập nhật bảng dữ liệu hiện tại
          tableBody.innerHTML = `
          <tr>
            <td>${currentDevice}</td>
            <td>${data.pm25 || "N/A"}</td>
            <td>${data.pm10 || "N/A"}</td>
            <td>${data.humidity || "N/A"}</td>
            <td>${data.temperature || "N/A"}</td>
          </tr>
        `;

          // Thêm hàng mới vào bảng lịch sử
          let newRow = document.createElement("tr");
          newRow.innerHTML = `
          <td>${currentTime}</td>
          <td>${data.temperature || "N/A"}</td>
          <td>${data.humidity || "N/A"}</td>
          <td>${data.pm25 || "N/A"}</td>
          <td>${data.pm10 || "N/A"}</td>
        `;
          sensorBody.prepend(newRow);

          // Giới hạn bảng lịch sử đến 50 hàng
          if (sensorBody.children.length > 50) {
            sensorBody.removeChild(sensorBody.lastChild);
          }

          // Cập nhật thời gian
          updateTimestamp();
        } catch (error) {
          console.error("Lỗi khi xử lý tin nhắn MQTT:", error);
        }
      });

      // Hàm đăng xuất
      function logout() {
        fetch("/logout", {
          method: "POST",
          credentials: "include",
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              window.location.href = "/";
            } else {
              alert("Đăng xuất thất bại: " + data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi:", error);
            alert("Đăng xuất thất bại. Vui lòng thử lại.");
          });
      }

      // Kiểm tra phiên khi tải trang
      window.onload = function () {
        // Hiển thị nội dung chính
        document.getElementById("mainContent").style.display = "block";

        fetch("/check_session", {
          credentials: "include",
        })
          .then((response) => {
            if (response.ok) {
              return response.json();
            } else {
              // Chuyển hướng về trang chủ nếu chưa đăng nhập
              window.location.href = "/";
            }
          })
          .then((data) => {
            if (data && data.username) {
              document.getElementById("currentUser").textContent = data.username;
              currentUser = data.username;
              isAdmin = data.isAdmin;

              if (isAdmin) {
                document.getElementById("adminPanel").style.display = "block";
              }

              // Kiểm tra nếu người dùng đã có thiết bị
              if (data.device_id) {
                currentDevice = data.device_id;
                document.getElementById("currentDevice").textContent = data.device_id;

                // Đăng ký nhận dữ liệu thiết bị
                subscribeToDevice(data.device_id);

                // Tải dữ liệu lịch sử MQTT
                setTimeout(loadMqttHistory, 1000); // Tải lịch sử sau một khoảng thời gian ngắn
              }
            }
          })
          .catch((error) => {
            console.error("Lỗi kiểm tra phiên:", error);
            window.location.href = "/";
          });
      };
    </script>
  </body>
</html>
