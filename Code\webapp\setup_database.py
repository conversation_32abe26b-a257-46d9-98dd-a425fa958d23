#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup Database - Tạo bảng thermal_data trong database
"""

import mysql.connector
import sys

def setup_database():
    """Tạo bảng thermal_data trong database"""
    try:
        # Kết nối database
        print("🔄 Đang kết nối đến database...")
        db = mysql.connector.connect(
            host="localhost",
            user="root",
            password="11z9*z3nAz",
            database="databasename"
        )
        cursor = db.cursor()
        print("✅ Kết nối database thành công")
        
        # Đ<PERSON><PERSON> và thực thi SQL
        print("🔄 Đang tạo bảng thermal_data...")
        
        # Tạo bảng thermal_data
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `thermal_data` (
          `id` int NOT NULL AUTO_INCREMENT,
          `device_id` varchar(50) NOT NULL DEFAULT 'default',
          `timestamp` bigint NOT NULL,
          `thermal_matrix` JSON NOT NULL,
          `min_temp` float NOT NULL,
          `max_temp` float NOT NULL,
          `avg_temp` float NOT NULL,
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_device_timestamp` (`device_id`, `timestamp`),
          KEY `idx_timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
        """
        
        cursor.execute(create_table_sql)
        
        # Tạo các index
        print("🔄 Đang tạo các index...")
        
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS `idx_device_created` ON `thermal_data` (`device_id`, `created_at`)")
        except mysql.connector.Error as e:
            if "Duplicate key name" not in str(e):
                print(f"⚠️ Cảnh báo khi tạo index: {e}")
        
        # Thêm comment cho bảng
        try:
            cursor.execute("ALTER TABLE `thermal_data` COMMENT = 'Lưu trữ dữ liệu nhiệt từ cảm biến MLX90640'")
        except mysql.connector.Error as e:
            print(f"⚠️ Cảnh báo khi thêm comment: {e}")
        
        db.commit()
        print("✅ Đã tạo bảng thermal_data thành công!")
        
        # Kiểm tra bảng đã tồn tại
        cursor.execute("SHOW TABLES LIKE 'thermal_data'")
        result = cursor.fetchone()
        
        if result:
            print("✅ Xác nhận: Bảng thermal_data đã tồn tại")
            
            # Hiển thị cấu trúc bảng
            cursor.execute("DESCRIBE thermal_data")
            columns = cursor.fetchall()
            
            print("\n📋 Cấu trúc bảng thermal_data:")
            print("-" * 60)
            print(f"{'Field':<20} {'Type':<20} {'Null':<8} {'Key':<8}")
            print("-" * 60)
            
            for column in columns:
                field, type_info, null, key, default, extra = column
                print(f"{field:<20} {type_info:<20} {null:<8} {key:<8}")
            
            print("-" * 60)
        else:
            print("❌ Lỗi: Bảng thermal_data không được tạo")
            return False
        
        cursor.close()
        db.close()
        
        print("\n🎉 Setup database hoàn thành!")
        print("📝 Bảng thermal_data đã sẵn sàng để lưu dữ liệu nhiệt")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Lỗi MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def check_database_connection():
    """Kiểm tra kết nối database"""
    try:
        print("🔄 Kiểm tra kết nối database...")
        db = mysql.connector.connect(
            host="localhost",
            user="root",
            password="11z9*z3nAz",
            database="databasename"
        )
        
        cursor = db.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        
        print(f"✅ Kết nối thành công! MySQL version: {version[0]}")
        
        cursor.close()
        db.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        print("💡 Hãy kiểm tra:")
        print("   - MySQL server có đang chạy không?")
        print("   - Username/password có đúng không?")
        print("   - Database 'databasename' có tồn tại không?")
        return False

def main():
    print("🗄️  DATABASE SETUP TOOL")
    print("=" * 50)
    print("Tạo bảng thermal_data để lưu dữ liệu nhiệt MLX90640")
    print("=" * 50)
    
    # Kiểm tra kết nối trước
    if not check_database_connection():
        print("\n❌ Không thể kết nối database. Vui lòng kiểm tra cấu hình.")
        sys.exit(1)
    
    print()
    
    # Xác nhận tạo bảng
    confirm = input("🚀 Tiếp tục tạo bảng thermal_data? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        success = setup_database()
        
        if success:
            print("\n✅ Setup hoàn thành! Bây giờ bạn có thể:")
            print("   1. Chạy thermal_capture.py để nhận dữ liệu MQTT")
            print("   2. Chạy server.py để khởi động web server")
            print("   3. Gửi dữ liệu test bằng mqtt_sender_full.py")
            print("   4. Xem ảnh nhiệt tại http://localhost:5000/mlx90640")
        else:
            print("\n❌ Setup thất bại!")
            sys.exit(1)
    else:
        print("❌ Đã hủy setup")

if __name__ == "__main__":
    main()
