<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32 Monitor - Welcome</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <style>
      #wrapper {
        min-height: 100vh;
        background: linear-gradient(135deg, rgba(78, 115, 223, 0.9), rgba(26, 32, 44, 0.9));
      }

      #header {
        padding: 1rem 2rem;
        background-color: rgba(255, 255, 255, 0.1);
      }

      #logo img {
        height: 50px;
        border-radius: 8px;
      }

      .container {
        padding-top: 3rem;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
      }

      .card h2 {
        color: var(--primary-color);
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
      }

      .card h3 {
        color: var(--dark-color);
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
      }

      .form-control {
        border-radius: 10px;
        padding: 0.75rem 1rem;
        border: 1px solid #e0e0e0;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
      }

      .form-control:focus {
        box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.25);
        border-color: var(--primary-color);
        background-color: #fff;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 10px;
        transition: all 0.3s ease;
      }

      .btn-primary {
        background-color: var(--primary-color);
        border: none;
      }

      .btn-primary:hover {
        background-color: #2e59d9;
        transform: translateY(-2px);
      }

      .btn-success {
        background-color: var(--secondary-color);
        border: none;
      }

      .btn-success:hover {
        background-color: #17a673;
        transform: translateY(-2px);
      }

      .shadow {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
      }

      .mb-3 {
        margin-bottom: 1.5rem !important;
      }

      .text-center {
        text-align: center;
      }

      .mt-3 {
        margin-top: 1.5rem !important;
      }

      .mx-2 {
        margin-left: 0.75rem !important;
        margin-right: 0.75rem !important;
      }

      @media (max-width: 768px) {
        .container {
          padding-top: 1rem;
        }

        .card {
          margin-bottom: 1rem;
        }

        .btn {
          width: 100%;
          margin: 0.5rem 0;
        }

        .d-flex {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <div id="wrapper">
      <div id="header">
        <nav>
          <a href="" id="logo"
            ><img src="/static/images/logo.jpg" alt="logo" />
          </a>
        </nav>
      </div>

      <div class="container mt-5">
        <div class="row">
          <div class="col-md-6">
            <div class="card p-4 shadow">
              <h2 class="text-center">Welcome to ESP32 Monitor</h2>
              <p class="text-center">Please login or register to continue</p>

              <div class="d-flex justify-content-center mt-3">
                <button class="btn btn-primary mx-2" onclick="showLoginForm()">
                  Login
                </button>
                <button
                  class="btn btn-success mx-2"
                  onclick="showRegisterForm()"
                >
                  Register
                </button>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <!-- Login Form -->
            <div class="card p-4 shadow" id="loginForm">
              <h3>Quick Login</h3>
              <div class="mb-3">
                <input
                  type="text"
                  id="loginUsername"
                  class="form-control"
                  placeholder="Username"
                />
              </div>
              <div class="mb-3">
                <input
                  type="password"
                  id="loginPassword"
                  class="form-control"
                  placeholder="Password"
                />
              </div>
              <button class="btn btn-success w-100" onclick="login()">
                Login
              </button>
            </div>

            <!-- Register Form (initially hidden) -->
            <div
              class="card p-4 shadow"
              id="registerForm"
              style="display: none"
            >
              <h3>Register New Account</h3>
              <div class="mb-3">
                <input
                  type="text"
                  id="registerUsername"
                  class="form-control"
                  placeholder="Username"
                />
              </div>
              <div class="mb-3">
                <input
                  type="password"
                  id="registerPassword"
                  class="form-control"
                  placeholder="Password"
                />
              </div>
              <div class="mb-3">
                <input
                  type="password"
                  id="confirmPassword"
                  class="form-control"
                  placeholder="Confirm Password"
                />
              </div>
              <button class="btn btn-primary w-100" onclick="register()">
                Register
              </button>
              <p class="text-center mt-3">
                <a href="#" onclick="showLoginForm(); return false;"
                  >Already have an account? Login</a
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  // doan nay de lam mlx90640
    <script>
      // Show login form, hide register form
      function showLoginForm() {
        document.getElementById("loginForm").style.display = "block";
        document.getElementById("registerForm").style.display = "none";
      }

      // Show register form, hide login form
      function showRegisterForm() {
        document.getElementById("loginForm").style.display = "none";
        document.getElementById("registerForm").style.display = "block";
      }

      // Login function
      function login() {
        const user = document.getElementById("loginUsername").value;
        const pass = document.getElementById("loginPassword").value;

        if (!user || !pass) {
          alert("Please enter both username and password");
          return;
        }

        fetch("/login", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ username: user, password: pass }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              window.location.href = "/home";
            } else {
              alert(data.message);
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            alert("Login failed. Please try again.");
          });
      }

      // Register function
      function register() {
        const user = document.getElementById("registerUsername").value;
        const pass = document.getElementById("registerPassword").value;
        const confirmPass = document.getElementById("confirmPassword").value;

        if (!user || !pass || !confirmPass) {
          alert("Please fill in all fields");
          return;
        }

        if (pass !== confirmPass) {
          alert("Passwords do not match");
          return;
        }

        fetch("/register", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({ username: user, password: pass }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              alert("Registration successful! Please login.");
              showLoginForm();
            } else {
              alert(data.message);
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            alert("Registration failed. Please try again.");
          });
      }
    </script>
  </body>
</html>
