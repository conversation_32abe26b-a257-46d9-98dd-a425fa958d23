import cv2
import numpy as np
import os
import json
import time
import paho.mqtt.client as mqtt
import socketio

# Đường dẫn đến thư mục lưu ảnh
IMAGE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static", "images")

def thermal_to_heatmap(thermal_data, colormap=cv2.COLORMAP_JET):
    """Chuyển mảng nhiệt sang ảnh heatmap màu"""
    # Chuẩn hóa dữ liệu nhiệt độ sang [0, 255]
    norm_data = cv2.normalize(thermal_data, None, 0, 255, cv2.NORM_MINMAX)
    norm_data = norm_data.astype(np.uint8)

    # Resize ảnh
    resized = cv2.resize(norm_data, (320, 240), interpolation=cv2.INTER_LINEAR)

    # Áp colormap để tạo heatmap
    heatmap = cv2.applyColorMap(resized, colormap)

    # Thêm thông tin nhiệt độ vào ảnh
    min_temp = np.min(thermal_data)
    max_temp = np.max(thermal_data)
    avg_temp = np.mean(thermal_data)

    # Thêm text vào ảnh
    cv2.putText(heatmap, f"Max: {max_temp:.1f}°C", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(heatmap, f"Min: {min_temp:.1f}°C", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(heatmap, f"Avg: {avg_temp:.1f}°C", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    return heatmap

def save_thermal_image(thermal_data, filename="temp.jpg"):
    """Lưu dữ liệu nhiệt thành ảnh heatmap"""
    if not os.path.exists(IMAGE_DIR):
        os.makedirs(IMAGE_DIR)

    heatmap = thermal_to_heatmap(thermal_data)

    filepath = os.path.join(IMAGE_DIR, filename)
    cv2.imwrite(filepath, heatmap)
    print(f"Đã lưu ảnh nhiệt vào {filepath}")

    return heatmap



# Biến toàn cục để lưu trữ các phần dữ liệu
thermal_chunks = {}

# Callback khi kết nối thành công đến MQTT broker
def on_connect(client, *_):
    print("Đã kết nối đến MQTT broker")
    client.subscribe("esp32/mlx90640")
    client.subscribe("esp32/mlx90640/chunk")
    print("Đã đăng ký nhận dữ liệu từ các topic MQTT")

# Callback khi nhận được tin nhắn từ MQTT broker
def on_message(_, __, msg):
    try:
        topic = msg.topic
        payload = json.loads(msg.payload.decode())

        # Xử lý dữ liệu đầy đủ (topic gốc)
        if topic == "esp32/mlx90640" and "data" in payload:
            thermal_data = np.array(payload["data"], dtype=np.float32)

            if thermal_data.size > 0:
                if len(thermal_data.shape) == 1:
                    thermal_data = thermal_data.reshape(24, 32)

                save_thermal_image(thermal_data)

                min_temp = np.min(thermal_data)
                max_temp = np.max(thermal_data)
                avg_temp = np.mean(thermal_data)
                print(f"Nhiệt độ: Min={min_temp:.1f}°C, Max={max_temp:.1f}°C, Avg={avg_temp:.1f}°C")
            else:
                print("Dữ liệu nhiệt rỗng")

        # Xử lý dữ liệu theo phần (chunks)
        elif topic == "esp32/mlx90640/chunk" and "data" in payload:
            chunk_id = payload.get("chunk_id")
            total_chunks = payload.get("total_chunks")
            row_index = payload.get("row_index", payload.get("start_row"))  # Hỗ trợ cả hai định dạng
            chunk_data = payload.get("data")

            if None in (chunk_id, total_chunks, row_index, chunk_data):
                print("Thiếu thông tin trong phần dữ liệu")
                return

            # Lưu phần dữ liệu vào bộ nhớ tạm
            key = f"session_{int(time.time() / 10)}"  # Tạo key cho phiên hiện tại (10 giây)
            if key not in thermal_chunks:
                thermal_chunks[key] = {"chunks": {}, "total": total_chunks, "received": 0}

            # Xử lý dữ liệu theo từng hàng
            if isinstance(chunk_data[0], list):
                # Định dạng cũ: mỗi phần là nhiều hàng
                thermal_chunks[key]["chunks"][chunk_id] = (row_index, chunk_data)
            else:
                # Định dạng mới: mỗi phần là một hàng
                thermal_chunks[key]["chunks"][chunk_id] = (row_index, [chunk_data])

            thermal_chunks[key]["received"] += 1

            print(f"Đã nhận phần {chunk_id+1}/{total_chunks}")

            # Kiểm tra xem đã nhận đủ các phần chưa
            if thermal_chunks[key]["received"] == total_chunks:
                print("Đã nhận đủ dữ liệu, đang tạo ảnh nhiệt...")

                # Tạo mảng dữ liệu đầy đủ
                full_data = np.zeros((24, 32), dtype=np.float32)

                # Ghép các phần dữ liệu
                for chunk_id, (row_idx, chunk_data) in thermal_chunks[key]["chunks"].items():
                    chunk_array = np.array(chunk_data, dtype=np.float32)
                    if len(chunk_array.shape) == 3:  # Nếu là mảng 3D (phần, hàng, cột)
                        chunk_array = chunk_array[0]  # Lấy phần đầu tiên
                    rows = len(chunk_array)
                    full_data[row_idx:row_idx+rows] = chunk_array



                # Vẫn lưu ảnh để backup (tùy chọn)
                # save_thermal_image(full_data)

                # Hiển thị thông tin nhiệt độ
                min_temp = np.min(full_data)
                max_temp = np.max(full_data)
                avg_temp = np.mean(full_data)
                print(f"Nhiệt độ: Min={min_temp:.1f}°C, Max={max_temp:.1f}°C, Avg={avg_temp:.1f}°C")

                # Xóa dữ liệu đã xử lý
                del thermal_chunks[key]

                # Dọn dẹp các phiên cũ
                current_time = int(time.time() / 10)
                for k in list(thermal_chunks.keys()):
                    session_time = int(k.split('_')[1])
                    if current_time - session_time > 6:  # Xóa phiên cũ hơn 60 giây
                        del thermal_chunks[k]
        else:
            print(f"Không tìm thấy dữ liệu nhiệt trong tin nhắn trên topic {topic}")
    except Exception as e:
        print(f"Lỗi khi xử lý tin nhắn: {e}")

def main():
    client = mqtt.Client()

    client.on_connect = on_connect
    client.on_message = on_message

    try:
        client.connect("broker.emqx.io", 1883, 60)
        print("Đang kết nối đến MQTT broker...")
    except Exception as e:
        print(f"Không thể kết nối đến MQTT broker: {e}")
        return

    try:
        print("Đang lắng nghe dữ liệu từ MLX90640... Nhấn Ctrl+C để thoát.")
        client.loop_forever()
    except KeyboardInterrupt:
        print("Đã dừng chương trình.")
    finally:
        client.disconnect()

if __name__ == "__main__":
    main()
