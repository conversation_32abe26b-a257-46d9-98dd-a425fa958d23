from flask import Flask, request, jsonify, session, render_template, make_response, send_file, Response
import json
from flask_cors import CORS
from datetime import timedelta
import os
import io
import cv2
import numpy as np
import paho.mqtt.client as mqtt
import mysql.connector
from flask_socketio import Socket<PERSON>, emit

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")
app.config.update(
    SECRET_KEY='your-secure-key-here',
    SESSION_COOKIE_SECURE=False,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(days=1),
    SESSION_COOKIE_PATH='/',
)

if app.debug:
    CORS(app,
         supports_credentials=True,
         resources={r"/*": {"origins": "http://localhost:5000"}})
else:
    CORS(app,
         supports_credentials=True,
         resources={r"/*": {
             "origins": ["https://yourdomain.com", "https://api.yourdomain.com"],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization"],
             "expose_headers": ["Content-Range", "X-Content-Range"],
             "max_age": 600
         }})

db = mysql.connector.connect(
    host="localhost",
    user="root",
    password="11z9*z3nAz",
    database="databasename"
)
cursor = db.cursor()

MQTT_BROKER = "broker.emqx.io"
MQTT_PORT = 1883
MQTT_TOPIC = "esp32/temp"
data_store = {}
active_users = set()

# Storage for 32 transmissions with 24 elements each
mqtt_data_history = {}  # Format: {device_id: [transmission1, transmission2, ...]}
MAX_HISTORY_SIZE = 32  # Maximum number of transmissions to store per device

# Storage for MLX90640 thermal data
mlx90640_data = {}  # Format: {device_id: {"timestamp": timestamp, "data": [24x32 array of temperatures]}}
mlx90640_history = {}  # Format: {device_id: [{"timestamp": timestamp, "max": max_temp, "min": min_temp, "avg": avg_temp}, ...]}
MAX_MLX_HISTORY_SIZE = 100  # Maximum number of historical data points to store

@socketio.on('connect')
def handle_connect():
    print('Client đã kết nối WebSocket')

@socketio.on('disconnect')
def handle_disconnect():
    print('Client đã ngắt kết nối WebSocket')

@socketio.on('new_thermal_image')
def handle_new_thermal_image(data):
    """Nhận thông báo ảnh mới và broadcast đến tất cả clients"""
    print(f"Nhận thông báo ảnh mới: {data['filename']}")
    emit('thermal_image_updated', data, broadcast=True)
def load_banned_ips():
    try:
        with open("banned_ips.json", "r") as file:
            return set(json.load(file))
    except (FileNotFoundError, json.JSONDecodeError):
        return set()

def save_banned_ips(banned_ips):
    with open("banned_ips.json", "w") as file:
        json.dump(list(banned_ips), file)

banned_ips = load_banned_ips()

def thermal_to_heatmap(thermal_data, colormap=cv2.COLORMAP_JET):
    """Chuyển mảng nhiệt sang ảnh heatmap màu"""
    # Chuyển đổi list thành numpy array
    if isinstance(thermal_data, list):
        thermal_data = np.array(thermal_data, dtype=np.float32)

    # Chuẩn hóa dữ liệu nhiệt độ sang [0, 255]
    norm_data = cv2.normalize(thermal_data, None, 0, 255, cv2.NORM_MINMAX)
    norm_data = norm_data.astype(np.uint8)

    # Resize ảnh để dễ nhìn hơn
    resized = cv2.resize(norm_data, (320, 240), interpolation=cv2.INTER_LINEAR)

    # Áp colormap để tạo heatmap
    heatmap = cv2.applyColorMap(resized, colormap)

    # Thêm thông tin nhiệt độ vào ảnh
    min_temp = np.min(thermal_data)
    max_temp = np.max(thermal_data)
    avg_temp = np.mean(thermal_data)

    # Thêm text vào ảnh
    cv2.putText(heatmap, f"Max: {max_temp:.1f}C", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(heatmap, f"Min: {min_temp:.1f}C", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(heatmap, f"Avg: {avg_temp:.1f}C", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    return heatmap



mqtt_client = mqtt.Client()

def on_connect(_, __, ___, ____):
    print("Connected to MQTT Broker")
    mqtt_client.subscribe("esp32/+/data")
    mqtt_client.subscribe("esp32/mlx90640")

def on_message(_, __, msg):
    topic = msg.topic

    try:
        payload = json.loads(msg.payload.decode())

        if topic == "esp32/mlx90640":
            process_mlx90640_data(payload)
            return

        parts = topic.split("/")
        if len(parts) >= 3 and parts[2] == "data":
            device_id = parts[1]

            if device_id in banned_ips:
                print(f"Blocked data from {device_id}")
                return

            if device_id not in data_store:
                data_store[device_id] = {}

            for key, value in payload.items():
                data_store[device_id][key] = value

            if device_id not in mqtt_data_history:
                mqtt_data_history[device_id] = []

            mqtt_data_history[device_id].append(payload)

            if len(mqtt_data_history[device_id]) > MAX_HISTORY_SIZE:
                mqtt_data_history[device_id].pop(0)

            print(f"Received data from {device_id}: {len(mqtt_data_history[device_id])} transmissions stored")

    except json.JSONDecodeError:
        print(f"Error decoding JSON from topic {topic}")
    except Exception as e:
        print(f"Error processing message from topic {topic}: {str(e)}")

def process_mlx90640_data(payload):
    """Process and store MLX90640 thermal data"""
    import time
    import statistics

    if not isinstance(payload, dict) or "data" not in payload:
        print("Invalid MLX90640 data format")
        return

    thermal_data = payload.get("data", [])

    if thermal_data and isinstance(thermal_data, list):
        flat_data = []
        if isinstance(thermal_data[0], list):
            for row in thermal_data:
                flat_data.extend(row)
        else:
            flat_data = thermal_data

        try:
            max_temp = max(flat_data)
            min_temp = min(flat_data)
            avg_temp = statistics.mean(flat_data)
            timestamp = int(time.time())

            mlx90640_data["default"] = {
                "timestamp": timestamp,
                "data": thermal_data,
                "max": max_temp,
                "min": min_temp,
                "avg": avg_temp
            }

            if "default" not in mlx90640_history:
                mlx90640_history["default"] = []

            mlx90640_history["default"].append({
                "timestamp": timestamp,
                "max": max_temp,
                "min": min_temp,
                "avg": avg_temp
            })

            if len(mlx90640_history["default"]) > MAX_MLX_HISTORY_SIZE:
                mlx90640_history["default"].pop(0)

            print(f"Processed MLX90640 data: {len(thermal_data)} elements, temp range: {min_temp:.1f}°C - {max_temp:.1f}°C")
        except Exception as e:
            print(f"Error calculating MLX90640 statistics: {str(e)}")
    else:
        print("Empty or invalid thermal data")
mqtt_client.on_connect = on_connect
mqtt_client.on_message = on_message
mqtt_client.connect("broker.emqx.io", 1883, 60)
mqtt_client.loop_start()

@app.route("/")
def index():
    return render_template("home.html")

@app.route("/login", methods=["GET"])
def login_page():
    return render_template("login.html")

@app.route("/register", methods=["GET"])
def register_page():
    return render_template("login.html")

@app.route("/register", methods=["POST"])
def register():
    data = request.get_json()
    username = data.get("username")
    password = data.get("password")

    cursor.execute("SELECT * FROM person WHERE username=%s", (username,))
    cursor.fetchall()
    if cursor.rowcount > 0:
        return jsonify({"status": "error", "message": "User already exists"}), 400

    cursor.execute("INSERT INTO person(username, password) VALUES (%s, %s)", (username, password))
    db.commit()
    return jsonify({"status": "success", "message": "User registered successfully"})

@app.route("/login", methods=["POST"])
def login():
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        cursor.execute("SELECT id FROM person WHERE username=%s AND password=%s", (username, password))
        user = cursor.fetchone()

        if user:
            session["username"] = username
            active_users.add(username)

            cursor.execute("SELECT device_id FROM devices WHERE owner=%s", (username,))
            devices = cursor.fetchall()
            device_id = devices[0][0] if devices else None

            response = make_response(jsonify({
                "status": "success",
                "message": "Login successful",
                "isAdmin": username == "admin",
                "device_id": device_id,
                "has_old_device": bool(device_id)
            }))

            # Thiết lập cookie với các tham số bảo mật
            response.set_cookie(
                'session_token',
                value=username,
                max_age=86400,  # 24 giờ
                httponly=True,  # Ngăn JavaScript truy cập cookie
                secure=True,   # Cho phép gửi qua HTTP trong môi trường phát triển
                samesite='Strict',  # Bảo vệ CSRF nhưng cho phép linh hoạt hơn
                path='/'
            )

            return response

        return jsonify({"status": "error", "message": "Invalid credentials"}), 401

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
@app.route("/logout", methods=["POST"])
def logout():
    if "username" in session:
        username = session["username"]
        active_users.discard(username)
        session.clear()

    response = make_response(jsonify({
        "status": "success",
        "message": "Logged out successfully"
    }))

    # Xóa tất cả các cookie liên quan
    response.delete_cookie('session_token', path='/')
    response.delete_cookie('username', path='/')

    # Đặt cookie hết hạn ngay lập tức
    response.set_cookie('session_token', '', expires=0,secure=True,httponly=True,samesite='Strict')
    response.set_cookie('username', '', expires=0,secure=True,httponly=True,samesite='Strict')

    return response
@app.route("/set_device", methods=["POST"])
def set_device():
    if "username" not in session:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    username = session["username"]
    data = request.get_json()
    device_id = data.get("device_id")

    cursor.execute("SELECT owner FROM devices WHERE device_id=%s", (device_id,))
    owner = cursor.fetchone()

    if owner and owner[0] != username:
        return jsonify({"status": "error", "message": "Device already owned by another user"})

    cursor.execute("INSERT INTO devices (device_id, owner) VALUES (%s, %s) ON DUPLICATE KEY UPDATE owner=%s", (device_id, username, username))
    db.commit()

    return jsonify({"status": "success", "message": "Device registered successfully"})

@app.route("/delete_device", methods=["POST"])
def delete_device():
    if "username" not in session or session["username"] != "admin":
        return jsonify({"status": "error", "message": "Permission denied"}), 403

    data = request.get_json()
    device_id = data.get("device_id")
    cursor.execute("DELETE FROM devices WHERE device_id=%s", (device_id,))
    db.commit()
    return jsonify({"status": "success", "message": "Device deleted successfully"})

@app.before_request
def check_session():
    # Các route không cần xác thực
    public_routes = ['login','register','login_page', 'register_page', 'static', 'index', 'home', 'check_user_session', 'mlx90640', 'get_temp_image']

    if request.endpoint not in public_routes:
        session_token = request.cookies.get('session_token')
        if not session_token:
            return jsonify({"status": "error", "message": "Unauthorized"}), 401

        if session_token not in active_users:
            return jsonify({"status": "error", "message": "Invalid session"}), 401

@app.route("/check_session", methods=["GET"])
def check_user_session():
    session_token = request.cookies.get('session_token')
    if session_token and session_token in active_users:
        # Get user's device if any
        cursor.execute("SELECT device_id FROM devices WHERE owner=%s", (session_token,))
        devices = cursor.fetchall()
        device_id = devices[0][0] if devices else None

        return jsonify({
            "status": "success",
            "message": "Valid session",
            "username": session_token,
            "isAdmin": session_token == "admin",
            "device_id": device_id
        })
    return jsonify({"status": "error", "message": "Unauthorized"}), 401
@app.route("/home", methods=["GET"])
def home():
    # Luôn trả về trang home.html, JavaScript sẽ xử lý hiển thị phù hợp dựa trên trạng thái đăng nhập
    return render_template("home.html")

@app.route("/mlx90640", methods=["GET"])
def mlx90640():
    # Luôn trả về trang mlx90640.html, JavaScript sẽ xử lý hiển thị phù hợp dựa trên trạng thái đăng nhập
    return render_template("mlx90640.html")

@app.route("/temp.jpg")
def get_temp_image():
    """Serve the temp.jpg image file"""
    try:
        image_path = os.path.join(app.static_folder, 'images', 'temp.jpg')

        if not os.path.exists(image_path):
            default_path = os.path.join(app.static_folder, 'images', 'default.jpg')
            if os.path.exists(default_path):
                return send_file(default_path, mimetype='image/jpeg')
            else:
                return "Image not found", 404

        return send_file(image_path, mimetype='image/jpeg')
    except Exception as e:
        print(f"Error serving temp.jpg: {str(e)}")
        return str(e), 500

@app.route("/devices", methods=["GET"])
def devices():
    # Luôn trả về trang devices.html, JavaScript sẽ xử lý hiển thị phù hợp dựa trên trạng thái đăng nhập
    return render_template("devices.html")

@app.route("/get_devices", methods=["GET"])
def get_devices():
    """Get all devices for the current user"""
    if "username" not in session:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    username = session["username"]

    # Admin can see all devices
    if username == "admin":
        cursor.execute("SELECT d.device_id, d.owner, MAX(h.timestamp) as last_seen FROM devices d LEFT JOIN mlx90640_history h ON d.device_id = h.device_id GROUP BY d.device_id, d.owner")
    else:
        cursor.execute("SELECT d.device_id, d.owner, MAX(h.timestamp) as last_seen FROM devices d LEFT JOIN mlx90640_history h ON d.device_id = h.device_id WHERE d.owner = %s GROUP BY d.device_id, d.owner", (username,))

    device_rows = cursor.fetchall()

    devices = []
    for row in device_rows:
        device_id = row[0]

        # Get latest data for this device
        device_data = data_store.get(device_id, {})

        devices.append({
            "device_id": device_id,
            "owner": row[1],
            "last_seen": row[2],
            "temperature": device_data.get("temperature"),
            "humidity": device_data.get("humidity"),
            "pm25": device_data.get("pm25"),
            "pm10": device_data.get("pm10")
        })

    return jsonify({
        "status": "success",
        "devices": devices
    })

@app.route("/get_mqtt_history", methods=["GET"])
def get_mqtt_history():
    if "username" not in session:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    device_id = request.args.get("device_id")
    if not device_id:
        return jsonify({"status": "error", "message": "Device ID is required"}), 400

    # Check if user owns this device
    username = session["username"]
    if username != "admin":  # Admin can access any device
        cursor.execute("SELECT owner FROM devices WHERE device_id=%s", (device_id,))
        owner = cursor.fetchone()
        if not owner or owner[0] != username:
            return jsonify({"status": "error", "message": "You don't have access to this device"}), 403

    # Get history data for the device
    history = mqtt_data_history.get(device_id, [])

    return jsonify({
        "status": "success",
        "device_id": device_id,
        "history_count": len(history),
        "history": history
    })

@app.route("/get_mlx90640_data", methods=["GET"])
def get_mlx90640_data():
    """Get the latest MLX90640 thermal data"""
    if "username" not in session:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    # Get thermal data using the fixed key
    thermal_data = mlx90640_data.get("default", {})

    if not thermal_data:
        return jsonify({
            "status": "error",
            "message": "No thermal data available"
        }), 404

    return jsonify({
        "status": "success",
        "timestamp": thermal_data.get("timestamp"),
        "data": thermal_data.get("data"),
        "max": thermal_data.get("max"),
        "min": thermal_data.get("min"),
        "avg": thermal_data.get("avg")
    })

@app.route("/get_mlx90640_history", methods=["GET"])
def get_mlx90640_history():
    """Get the historical MLX90640 thermal data"""
    if "username" not in session:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    # Get thermal history using the fixed key
    history = mlx90640_history.get("default", [])

    return jsonify({
        "status": "success",
        "history_count": len(history),
        "history": history
    })

@app.route("/thermal_image.jpg", methods=["GET"])
def get_thermal_image():
    """Tạo và trả về ảnh nhiệt từ database"""
    try:
        # Lấy dữ liệu nhiệt từ memory
        thermal_data = mlx90640_data.get("default", {})

        if not thermal_data or "data" not in thermal_data:
            # Nếu không có dữ liệu, tạo ảnh mặc định
            default_data = np.ones((24, 32), dtype=np.float32) * 25.0
            heatmap = thermal_to_heatmap(default_data)
        else:
            # Tạo ảnh từ dữ liệu thực
            thermal_matrix = thermal_data["data"]
            heatmap = thermal_to_heatmap(thermal_matrix)

        # Chuyển đổi ảnh thành bytes
        _, buffer = cv2.imencode('.jpg', heatmap)
        img_bytes = io.BytesIO(buffer)

        return Response(
            img_bytes.getvalue(),
            mimetype='image/jpeg',
            headers={
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )

    except Exception as e:
        print(f"Lỗi khi tạo ảnh nhiệt: {e}")
        # Trả về ảnh lỗi
        error_img = np.zeros((240, 320, 3), dtype=np.uint8)
        cv2.putText(error_img, "Error loading thermal data", (10, 120),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        _, buffer = cv2.imencode('.jpg', error_img)
        img_bytes = io.BytesIO(buffer)

        return Response(img_bytes.getvalue(), mimetype='image/jpeg')

@app.route("/get_thermal_data", methods=["GET"])
def get_thermal_data_api():
    """API để lấy dữ liệu nhiệt mới nhất"""
    try:
        thermal_data = mlx90640_data.get("default", {})

        if thermal_data and "data" in thermal_data:
            return jsonify({
                "status": "success",
                "data": thermal_data["data"],
                "min_temp": thermal_data.get("min", 0),
                "max_temp": thermal_data.get("max", 0),
                "avg_temp": thermal_data.get("avg", 0),
                "timestamp": thermal_data.get("timestamp", 0)
            })
        else:
            return jsonify({
                "status": "error",
                "message": "Không có dữ liệu nhiệt"
            })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Lỗi khi lấy dữ liệu: {str(e)}"
        })

if __name__ == "__main__":
# socketio.run(app, debug=True, host="0.0.0.0", port=5000)
    app.run(debug=True, host="0.0.0.0", port=5000)  # 