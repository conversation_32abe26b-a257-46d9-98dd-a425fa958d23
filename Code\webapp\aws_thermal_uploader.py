#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS S3 Thermal Image Uploader
N<PERSON>ận dữ liệu nhiệt từ MQTT và upload ảnh lên AWS S3
"""

import cv2
import numpy as np
import json
import time
import boto3
import paho.mqtt.client as mqtt
from datetime import datetime
import io
import os
from botocore.exceptions import ClientError, NoCredentialsError

# Cấu hình AWS S3
AWS_ACCESS_KEY_ID = "YOUR_ACCESS_KEY_ID"
AWS_SECRET_ACCESS_KEY = "YOUR_SECRET_ACCESS_KEY"
AWS_REGION = "ap-southeast-1"  # Singapore region
S3_BUCKET_NAME = "your-thermal-bucket"

# Cấu hình MQTT
MQTT_BROKER = "broker.emqx.io"
MQTT_PORT = 1883
MQTT_TOPIC_CHUNK = "esp32/mlx90640/chunk"
MQTT_TOPIC_FULL = "esp32/mlx90640"

class ThermalS3Uploader:
    def __init__(self):
        self.thermal_chunks = {}
        self.s3_client = None
        self.mqtt_client = None
        self.init_aws_s3()
        self.init_mqtt()

    def init_aws_s3(self):
        """Khởi tạo kết nối AWS S3"""
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                region_name=AWS_REGION
            )

            # Test kết nối bằng cách list buckets
            self.s3_client.list_buckets()
            print("Kết nối AWS S3 thành công")

            # Kiểm tra bucket có tồn tại không
            try:
                self.s3_client.head_bucket(Bucket=S3_BUCKET_NAME)
                print(f"Bucket '{S3_BUCKET_NAME}' đã tồn tại")
            except ClientError as e:
                error_code = int(e.response['Error']['Code'])
                if error_code == 404:
                    print(f"Bucket '{S3_BUCKET_NAME}' không tồn tại. Tạo bucket mới...")
                    self.create_bucket()
                else:
                    print(f"Lỗi kiểm tra bucket: {e}")

        except NoCredentialsError:
            print("Không tìm thấy AWS credentials. Vui lòng cấu hình:")
            print("   1. Sửa AWS_ACCESS_KEY_ID và AWS_SECRET_ACCESS_KEY trong file")
            print("   2. Hoặc cấu hình AWS CLI: aws configure")
            self.s3_client = None
        except Exception as e:
            print(f"Lỗi kết nối AWS S3: {e}")
            self.s3_client = None

    def create_bucket(self):
        """Tạo S3 bucket mới"""
        try:
            if AWS_REGION == 'us-east-1':
                self.s3_client.create_bucket(Bucket=S3_BUCKET_NAME)
            else:
                self.s3_client.create_bucket(
                    Bucket=S3_BUCKET_NAME,
                    CreateBucketConfiguration={'LocationConstraint': AWS_REGION}
                )
            print(f"Đã tạo bucket '{S3_BUCKET_NAME}' thành công")
        except Exception as e:
            print(f"Lỗi tạo bucket: {e}")

    def init_mqtt(self):
        """Khởi tạo MQTT client"""
        self.mqtt_client = mqtt.Client()
        self.mqtt_client.on_connect = self.on_connect
        self.mqtt_client.on_message = self.on_message

    def thermal_to_heatmap(self, thermal_data, colormap=cv2.COLORMAP_JET):
        """Chuyển mảng nhiệt sang ảnh heatmap màu"""
        # Chuẩn hóa dữ liệu nhiệt độ sang [0, 255]
        norm_data = cv2.normalize(thermal_data, None, 0, 255, cv2.NORM_MINMAX)
        norm_data = norm_data.astype(np.uint8)

        # Resize ảnh để dễ xem
        resized = cv2.resize(norm_data, (320, 240), interpolation=cv2.INTER_LINEAR)

        # Áp colormap để tạo heatmap
        heatmap = cv2.applyColorMap(resized, colormap)

        # Thêm thông tin nhiệt độ vào ảnh
        min_temp = np.min(thermal_data)
        max_temp = np.max(thermal_data)
        avg_temp = np.mean(thermal_data)

        # Thêm text vào ảnh
        cv2.putText(heatmap, f"Max: {max_temp:.1f}C", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(heatmap, f"Min: {min_temp:.1f}C", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(heatmap, f"Avg: {avg_temp:.1f}C", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Thêm timestamp
        timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(heatmap, timestamp_str, (10, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        return heatmap

    def upload_thermal_image_to_s3(self, thermal_data, device_id="esp32"):
        """Upload ảnh nhiệt lên S3"""
        if not self.s3_client:
            print("❌ Không có kết nối S3")
            return False

        try:
            # Tạo ảnh heatmap
            heatmap = self.thermal_to_heatmap(thermal_data)

            # Chuyển ảnh thành bytes
            _, img_encoded = cv2.imencode('.jpg', heatmap)
            img_bytes = img_encoded.tobytes()

            # Tạo tên file với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"thermal_images/{device_id}/{timestamp}.jpg"

            # Upload lên S3
            self.s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=filename,
                Body=img_bytes,
                ContentType='image/jpeg',
                Metadata={
                    'device_id': device_id,
                    'timestamp': str(int(time.time())),
                    'min_temp': str(np.min(thermal_data)),
                    'max_temp': str(np.max(thermal_data)),
                    'avg_temp': str(np.mean(thermal_data))
                }
            )

            # Tạo URL để truy cập ảnh
            s3_url = f"https://{S3_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{filename}"

            print(f"Đã upload ảnh nhiệt lên S3: {filename}")
            print(f"URL: {s3_url}")

            return True

        except Exception as e:
            print(f"Lỗi upload ảnh lên S3: {e}")
            return False

    def upload_thermal_data_json_to_s3(self, thermal_data, device_id="esp32"):
        """Upload dữ liệu nhiệt dạng JSON lên S3"""
        if not self.s3_client:
            print("Không có kết nối S3")
            return False

        try:
            # Tạo dữ liệu JSON
            thermal_json = {
                "device_id": device_id,
                "timestamp": int(time.time()),
                "datetime": datetime.now().isoformat(),
                "thermal_matrix": thermal_data.tolist(),
                "statistics": {
                    "min_temp": float(np.min(thermal_data)),
                    "max_temp": float(np.max(thermal_data)),
                    "avg_temp": float(np.mean(thermal_data))
                },
                "dimensions": {
                    "rows": thermal_data.shape[0],
                    "cols": thermal_data.shape[1]
                }
            }

            # Chuyển thành JSON string
            json_data = json.dumps(thermal_json, indent=2)

            # Tạo tên file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"thermal_data/{device_id}/{timestamp}.json"

            # Upload lên S3
            self.s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=filename,
                Body=json_data,
                ContentType='application/json',
                Metadata={
                    'device_id': device_id,
                    'timestamp': str(int(time.time()))
                }
            )

            print(f"Đã upload dữ liệu JSON lên S3: {filename}")

            return True

        except Exception as e:
            print(f"Lỗi upload JSON lên S3: {e}")
            return False

    def on_connect(self, client, *_):
        """Callback khi kết nối MQTT thành công"""
        print("Đã kết nối đến MQTT broker")
        client.subscribe(MQTT_TOPIC_CHUNK)
        client.subscribe(MQTT_TOPIC_FULL)
        print(f"Đã đăng ký nhận dữ liệu từ: {MQTT_TOPIC_CHUNK}, {MQTT_TOPIC_FULL}")

    def on_message(self, _, __, msg):
        """Callback khi nhận được tin nhắn MQTT"""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())

            # Xử lý dữ liệu đầy đủ (topic gốc)
            if topic == MQTT_TOPIC_FULL and "data" in payload:
                thermal_data = np.array(payload["data"], dtype=np.float32)

                if thermal_data.size > 0:
                    if len(thermal_data.shape) == 1:
                        thermal_data = thermal_data.reshape(24, 32)

                    print(f"Nhận dữ liệu đầy đủ từ {topic}")
                    self.process_complete_thermal_data(thermal_data)
                else:
                    print("Dữ liệu nhiệt rỗng")

            # Xử lý dữ liệu theo chunks
            elif topic == MQTT_TOPIC_CHUNK and "data" in payload:
                self.process_thermal_chunk(payload)

            else:
                print(f"Không tìm thấy dữ liệu nhiệt trong tin nhắn từ {topic}")

        except Exception as e:
            print(f"Lỗi xử lý tin nhắn MQTT: {e}")

    def process_thermal_chunk(self, payload):
        """Xử lý dữ liệu nhiệt theo chunks"""
        chunk_id = payload.get("chunk_id")
        total_chunks = payload.get("total_chunks")
        row_index = payload.get("row_index", payload.get("start_row"))
        chunk_data = payload.get("data")

        if None in (chunk_id, total_chunks, row_index, chunk_data):
            print("Thiếu thông tin trong chunk dữ liệu")
            return

        # Tạo session key
        session_key = f"session_{int(time.time() / 10)}"

        if session_key not in self.thermal_chunks:
            self.thermal_chunks[session_key] = {
                "chunks": {},
                "total": total_chunks,
                "received": 0
            }

        # Lưu chunk data
        self.thermal_chunks[session_key]["chunks"][chunk_id] = (row_index, chunk_data)
        self.thermal_chunks[session_key]["received"] += 1

        print(f"📦 Nhận chunk {chunk_id+1}/{total_chunks}")

        # Kiểm tra đã nhận đủ chunks chưa
        if self.thermal_chunks[session_key]["received"] == total_chunks:
            print("🔄 Đã nhận đủ chunks, đang xử lý...")
            self.assemble_and_upload_thermal_data(session_key)

    def assemble_and_upload_thermal_data(self, session_key):
        """Ghép các chunks thành dữ liệu đầy đủ và upload"""
        try:
            # Tạo mảng dữ liệu đầy đủ
            full_data = np.zeros((24, 32), dtype=np.float32)

            # Ghép các chunks
            for chunk_id, (row_idx, chunk_data) in self.thermal_chunks[session_key]["chunks"].items():
                if isinstance(chunk_data[0], list):
                    # Định dạng cũ: nhiều hàng
                    chunk_array = np.array(chunk_data, dtype=np.float32)
                else:
                    # Định dạng mới: một hàng
                    chunk_array = np.array([chunk_data], dtype=np.float32)

                rows = len(chunk_array)
                full_data[row_idx:row_idx+rows] = chunk_array

            # Xử lý dữ liệu hoàn chỉnh
            self.process_complete_thermal_data(full_data)

            # Xóa session đã xử lý
            del self.thermal_chunks[session_key]

            # Dọn dẹp sessions cũ
            self.cleanup_old_sessions()

        except Exception as e:
            print(f" Lỗi ghép chunks: {e}")

    def process_complete_thermal_data(self, thermal_data):
        """Xử lý dữ liệu nhiệt hoàn chỉnh"""
        min_temp = np.min(thermal_data)
        max_temp = np.max(thermal_data)
        avg_temp = np.mean(thermal_data)

        print(f"  Nhiệt độ: Min={min_temp:.1f}°C, Max={max_temp:.1f}°C, Avg={avg_temp:.1f}°C")

        # Upload ảnh lên S3
        self.upload_thermal_image_to_s3(thermal_data)

        # Upload dữ liệu JSON lên S3 (tùy chọn)
        self.upload_thermal_data_json_to_s3(thermal_data)

    def cleanup_old_sessions(self):
        """Dọn dẹp các sessions cũ"""
        current_time = int(time.time() / 10)
        for key in list(self.thermal_chunks.keys()):
            session_time = int(key.split('_')[1])
            if current_time - session_time > 6:  # Xóa session cũ hơn 60 giây
                del self.thermal_chunks[key]

    def start(self):
        """Bắt đầu chương trình"""
        if not self.s3_client:
            print(" Không thể khởi động do lỗi kết nối S3")
            return

        try:
            print(f" Đang kết nối đến MQTT broker: {MQTT_BROKER}:{MQTT_PORT}")
            self.mqtt_client.connect(MQTT_BROKER, MQTT_PORT, 60)

            print(" Đang lắng nghe dữ liệu nhiệt từ MQTT...")
            print(" Sẽ upload ảnh nhiệt lên AWS S3")
            print(" Nhấn Ctrl+C để dừng")

            self.mqtt_client.loop_forever()

        except KeyboardInterrupt:
            print("\n  Đã dừng chương trình")
        except Exception as e:
            print(f" Lỗi kết nối MQTT: {e}")
        finally:
            if self.mqtt_client:
                self.mqtt_client.disconnect()

def main():
    print(" AWS S3 Thermal Image Uploader")
    print("=" * 50)

    # Kiểm tra cấu hình AWS
    if AWS_ACCESS_KEY_ID == "YOUR_ACCESS_KEY_ID":
        print("  CẢNH BÁO: Chưa cấu hình AWS credentials!")
        print(" Vui lòng sửa các thông tin sau trong file:")
        print(f"   - AWS_ACCESS_KEY_ID")
        print(f"   - AWS_SECRET_ACCESS_KEY")
        print(f"   - S3_BUCKET_NAME")
        print(f"   - AWS_REGION")
        return

    uploader = ThermalS3Uploader()
    uploader.start()

if __name__ == "__main__":
    main()
