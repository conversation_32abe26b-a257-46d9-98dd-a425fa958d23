<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32 Monitor - Trang <PERSON>ủ</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
  </head>
  <body>
    <!-- Navbar cho người dùng chưa đăng nhập -->
    <nav id="guestNavbar" class="navbar">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="#features" class="nav-link">Tính Năng</a>
        </li>
        <li class="nav-item"><a href="#about" class="nav-link">Giới Thiệu</a></li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link">MLX90640</a>
        </li>
      </ul>

      <div class="navbar-right">
        <a href="/login" class="btn btn-primary">Đăng Nhập</a>
      </div>
    </nav>

    <!-- Navbar cho người dùng đã đăng nhập -->
    <nav id="userNavbar" class="navbar" style="display: none;">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/home" class="nav-link">
            <i class="fas fa-tachometer-alt"></i> Bảng Điều Khiển
          </a>
        </li>
        <li class="nav-item">
          <a href="/devices" class="nav-link">
            <i class="fas fa-microchip"></i> Thiết Bị
          </a>
        </li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link">
            <i class="fas fa-fire"></i> Ảnh Tầm Nhiệt
          </a>
        </li>

      </ul>

      <div class="navbar-right">
        <button class="btn btn-danger" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i> Đăng Xuất
        </button>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <h1>Giám Sát Môi Trường Với ESP32</h1>
        <p>
          Theo dõi nhiệt độ, độ ẩm và chất lượng không khí theo thời gian thực với
          giải pháp dựa trên ESP32 của chúng tôi
        </p>
        <a href="/login" class="btn btn-primary">Bắt Đầu Ngay</a>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="container mt-5">
      <h2 class="text-center mb-4">Tính Năng Chính</h2>
      <div class="row">
        <div class="col-4">
          <div class="card feature-card">
            <div class="card-body">
              <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
              <h3>Giám Sát Thời Gian Thực</h3>
              <p>
                Theo dõi dữ liệu môi trường theo thời gian thực với cập nhật và
                thông báo tự động
              </p>
            </div>
          </div>
        </div>
        <div class="col-4">
          <div class="card feature-card">
            <div class="card-body">
              <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
              <h3>Truy Cập Từ Xa</h3>
              <p>
                Truy cập dữ liệu của bạn từ bất kỳ đâu bằng giao diện web hoặc
                ứng dụng di động của chúng tôi
              </p>
            </div>
          </div>
        </div>
        <div class="col-4">
          <div class="card feature-card">
            <div class="card-body">
              <div class="feature-icon"><i class="fas fa-database"></i></div>
              <h3>Phân Tích Dữ Liệu</h3>
              <p>
                Phân tích xu hướng và mẫu với các công cụ phân tích tích hợp
                của chúng tôi
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="container mt-5">
      <h2 class="text-center mb-4">Giới Thiệu Về Giải Pháp Của Chúng Tôi</h2>
      <div class="row">
        <div class="col-6">
          <img
            src="/static/images/esp32.jpg"
            alt="Thiết bị ESP32"
            class="img-fluid rounded"
          />
        </div>
        <div class="col-6">
          <h3>ESP32 Là Gì?</h3>
          <p>
            ESP32 là một vi điều khiển mạnh mẽ với khả năng Wi-Fi và
            Bluetooth tích hợp, làm cho nó hoàn hảo cho các ứng dụng IoT.
          </p>

          <h3 class="mt-4">Hệ Thống Giám Sát Của Chúng Tôi</h3>
          <p>
            Hệ thống của chúng tôi sử dụng các thiết bị ESP32 được trang bị cảm biến chất lượng cao để
            giám sát các điều kiện môi trường bao gồm:
          </p>
          <ul>
            <li>Nhiệt độ và độ ẩm</li>
            <li>Chất lượng không khí (PM2.5 và PM10)</li>
            <li>Mức CO2</li>
            <li>Cường độ ánh sáng</li>
          </ul>
          <p>
            Dữ liệu được truyền an toàn đến nền tảng đám mây của chúng tôi, nơi bạn có thể
            truy cập bất cứ lúc nào, bất cứ nơi đâu.
          </p>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="container mt-5">
      <h2 class="text-center mb-4">Cách Thức Hoạt Động</h2>
      <div class="row">
        <div class="col-3">
          <div class="card text-center p-3">
            <div class="feature-icon"><i class="fas fa-microchip"></i></div>
            <h3>1. Thu Thập</h3>
            <p>Cảm biến ESP32 thu thập dữ liệu môi trường</p>
          </div>
        </div>
        <div class="col-3">
          <div class="card text-center p-3">
            <div class="feature-icon"><i class="fas fa-wifi"></i></div>
            <h3>2. Truyền Tải</h3>
            <p>Dữ liệu được truyền an toàn đến đám mây của chúng tôi</p>
          </div>
        </div>
        <div class="col-3">
          <div class="card text-center p-3">
            <div class="feature-icon"><i class="fas fa-server"></i></div>
            <h3>3. Xử Lý</h3>
            <p>Máy chủ của chúng tôi xử lý và phân tích dữ liệu</p>
          </div>
        </div>
        <div class="col-3">
          <div class="card text-center p-3">
            <div class="feature-icon"><i class="fas fa-laptop"></i></div>
            <h3>4. Truy Cập</h3>
            <p>Truy cập thông tin từ bất kỳ thiết bị nào</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="hero-section mt-5" style="padding: 4rem 0">
      <div class="container text-center">
        <h2>Sẵn Sàng Bắt Đầu?</h2>
        <p class="mb-4">
          Tham gia cùng hàng nghìn người dùng đang giám sát môi trường của họ
          với ESP32
        </p>
        <a href="/login" class="btn btn-primary btn-lg">Đăng Ký Ngay</a>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="row">
          <div class="col-4">
            <h3>ESP32 Monitor</h3>
            <p>
              Giải pháp giám sát môi trường tiên tiến cho nhà ở và
              doanh nghiệp.
            </p>
          </div>
          <div class="col-4">
            <h3>Liên Kết Nhanh</h3>
            <ul>
              <li><a href="#features">Tính Năng</a></li>
              <li><a href="#about">Giới Thiệu</a></li>
              <li><a href="/mlx90640">MLX90640</a></li>
              <li><a href="/login" id="loginLink">Đăng Nhập</a></li>
              <li><a href="/devices" id="devicesLink" style="display: none;">Thiết Bị</a></li>
            </ul>
          </div>
          <div class="col-4">
            <h3>Liên Hệ</h3>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +84 123 456 789</p>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-linkedin"></i></a>
            </div>
          </div>
        </div>
        <div class="text-center mt-4">
          <p>&copy; 2023 ESP32 Monitor. Đã đăng ký bản quyền.</p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script>
      // Biến toàn cục
      let isLoggedIn = false;
      let currentUser = "";

      // Đăng xuất
      function logout() {
        fetch("/logout", {
          method: "POST",
          credentials: "include",
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              window.location.href = "/";
            } else {
              alert("Đăng xuất thất bại: " + data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi:", error);
            alert("Đăng xuất thất bại. Vui lòng thử lại.");
          });
      }

      // Cuộn mượt cho các liên kết điều hướng
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();

          const targetId = this.getAttribute("href");
          if (targetId === "#") return;

          const targetElement = document.querySelector(targetId);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: "smooth" });
          }
        });
      });

      // Kiểm tra phiên đăng nhập khi trang tải xong
      window.onload = function () {
        // Kiểm tra phiên đăng nhập
        fetch("/check_session", {
          credentials: "include",
        })
          .then((response) => {
            if (response.ok) {
              return response.json();
            } else {
              // Người dùng chưa đăng nhập
              isLoggedIn = false;
              document.getElementById("guestNavbar").style.display = "flex";
              document.getElementById("userNavbar").style.display = "none";
            }
          })
          .then((data) => {
            if (data && data.status === "success") {
              // Người dùng đã đăng nhập
              isLoggedIn = true;
              currentUser = data.username;

              // Hiển thị navbar cho người dùng đã đăng nhập
              document.getElementById("guestNavbar").style.display = "none";
              document.getElementById("userNavbar").style.display = "flex";

              // Cập nhật liên kết trong footer
              document.getElementById("loginLink").style.display = "none";
              document.getElementById("devicesLink").style.display = "block";
            }
          })
          .catch((error) => {
            console.error("Lỗi kiểm tra phiên:", error);
          });

        // Kiểm tra hash URL cho đăng nhập hoặc đăng ký
        if (window.location.hash === "#login") {
          window.location.href = "/login";
        } else if (window.location.hash === "#register") {
          window.location.href = "/login#register";
        }
      };
    </script>
  </body>
</html>
