# Hướng dẫn cấu hình AWS S3 Thermal Uploader

## 1. Cài đặt dependencies

```bash
pip install -r requirements_aws.txt
```

## 2. Tạo AWS Account và S3 Bucket

### Bước 1: Tạo AWS Account

- T<PERSON>y cập https://aws.amazon.com/
- Đăng ký tài khoản AWS (cần thẻ tín dụng)

### Bước 2: Tạo IAM User

1. Vào AWS Console → IAM → Users → Create User
2. Tên user: `thermal-uploader`
3. Attach policies: `AmazonS3FullAccess`
4. Tạo Access Key → Download CSV file

### Bước 3: Tạo S3 Bucket

1. Vào AWS Console → S3 → Create Bucket
2. Tên bucket: `your-thermal-bucket` (phải unique toàn cầu)
3. Region: `ap-southeast-1` (Singapore)
4. Đ<PERSON> mặc định các cài đặt khác

## 3. C<PERSON><PERSON> hình file aws_thermal_uploader.py

Sửa các thông tin sau trong file:

```python
# Cấu hình AWS S3
AWS_ACCESS_KEY_ID = "AKIA..."           # Từ IAM User
AWS_SECRET_ACCESS_KEY = "abc123..."     # Từ IAM User
AWS_REGION = "ap-southeast-1"           # Region của bucket
S3_BUCKET_NAME = "your-thermal-bucket"  # Tên bucket đã tạo
```

## 4. Chạy chương trình

```bash
python aws_thermal_uploader.py
```

## 5. Cấu trúc dữ liệu trên S3

```
your-thermal-bucket/
├── thermal_images/
│   └── esp32/
│       ├── 20241201_143022.jpg
│       ├── 20241201_143045.jpg
│       └── ...
└── thermal_data/
    └── esp32/
        ├── 20241201_143022.json
        ├── 20241201_143045.json
        └── ...
```

## 6. Truy cập ảnh từ S3

URL format: `https://your-thermal-bucket.s3.ap-southeast-1.amazonaws.com/thermal_images/esp32/20241201_143022.jpg`

## 7. Chi phí AWS S3

- **PUT requests**: $0.005 per 1,000 requests
- **Storage**: $0.023 per GB/month
- **Data transfer**: Free cho 1GB đầu tiên/tháng

Ước tính: ~$1-5/tháng cho việc upload ảnh nhiệt thường xuyên.

## 8. Troubleshooting

### Lỗi "NoCredentialsError"

- Kiểm tra AWS_ACCESS_KEY_ID và AWS_SECRET_ACCESS_KEY
- Hoặc cấu hình AWS CLI: `aws configure`

### Lỗi "BucketNotFound"

- Kiểm tra tên bucket có đúng không
- Kiểm tra region có đúng không

### Lỗi "AccessDenied"

- Kiểm tra IAM user có quyền S3FullAccess
- Kiểm tra bucket policy

## 9. Bảo mật

⚠️ **QUAN TRỌNG**:

- Không commit AWS credentials vào Git
- Sử dụng environment variables trong production
- Rotate Access Keys định kỳ
