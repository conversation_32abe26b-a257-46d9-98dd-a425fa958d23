<!DOCTYPE html>
<html lang="vi">
  <head>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESP32 Monitor - Ảnh Tầm Nhiệt MLX90640</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mqtt/4.3.7/mqtt.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/heatmap.js@2.0.5/build/heatmap.min.js"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
      .thermal-container {
        position: relative;
        width: 100%;
        max-width: 640px;
        margin: 0 auto;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      #thermal-image {
        width: 100%;
        height: 480px;
        background-color: #000;
      }

      .thermal-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 14px;
      }

      .thermal-controls {
        background-color: #f8f9fa;
        padding: 15px;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }

      .thermal-legend {
        display: flex;
        justify-content: center;
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
      }

      .legend-item {
        display: flex;
        align-items: center;
        margin: 0 10px;
      }

      .legend-color {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        border-radius: 3px;
      }

      .stats-card {
        background-color: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .stats-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }

      .stats-value {
        font-size: 24px;
        font-weight: 700;
        color: #4e73df;
      }

      .temperature-chart-container {
        height: 300px;
        margin-top: 20px;
      }

      .nav-tabs .nav-link {
        color: #555;
        font-weight: 600;
      }

      .nav-tabs .nav-link.active {
        color: #4e73df;
        font-weight: 700;
      }

      .tab-content {
        padding: 20px;
        background-color: white;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
      }
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        font-weight: bold;
        animation: slideIn 0.3s ease-out;
      }

      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .thermal-stats {
        display: flex;
        gap: 20px;
        margin-top: 10px;
        font-size: 16px;
        font-weight: bold;
      }

      .thermal-stats span {
        padding: 5px 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <!-- Navbar cho người dùng chưa đăng nhập -->
    <nav id="guestNavbar" class="navbar">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/#features" class="nav-link">Tính Năng</a>
        </li>
        <li class="nav-item">
          <a href="/#about" class="nav-link">Giới Thiệu</a>
        </li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link active">MLX90640</a>
        </li>
      </ul>

      <div class="navbar-right">
        <a href="/login" class="btn btn-primary">Đăng Nhập</a>
      </div>
    </nav>

    <!-- Navbar cho người dùng đã đăng nhập -->
    <nav id="userNavbar" class="navbar" style="display: none">
      <a href="/" class="navbar-brand">
        <img src="/static/images/logo.jpg" alt="ESP32 Monitor Logo" />
        <span>ESP32 Monitor</span>
      </a>

      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/home" class="nav-link">
            <i class="fas fa-tachometer-alt"></i> Bảng Điều Khiển
          </a>
        </li>
        <li class="nav-item">
          <a href="/devices" class="nav-link">
            <i class="fas fa-microchip"></i> Thiết Bị
          </a>
        </li>
        <li class="nav-item">
          <a href="/mlx90640" class="nav-link active">
            <i class="fas fa-fire"></i> Ảnh Tầm Nhiệt
          </a>
        </li>
      </ul>

      <div class="navbar-right">
        <button class="btn btn-danger" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i> Đăng Xuất
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <div id="mainContent" style="display: none">
      <!-- Welcome Header -->
      <div class="card mb-4">
        <div
          class="card-body d-flex justify-content-between align-items-center"
        >
          <div>
            <h1 class="mb-0">
              Xin chào, <span id="currentUser" class="text-primary"></span>!
            </h1>
            <p class="text-muted">
              Theo dõi dữ liệu tầm nhiệt từ cảm biến MLX90640
            </p>
          </div>
          <div>
            <span class="badge bg-success p-2 mb-2 d-block">
              <i class="fas fa-circle"></i> Hệ Thống Hoạt Động
            </span>
            <span class="text-muted small">
              Cập nhật lần cuối: <span id="lastUpdate">Vừa xong</span>
            </span>
          </div>
        </div>
      </div>

      <!-- Thermal Image Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Ảnh Tầm Nhiệt MLX90640</h5>
              <div>
                <button class="btn btn-sm btn-primary" id="captureBtn">
                  <i class="fas fa-camera"></i> Chụp Ảnh
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-8">
                  <div class="thermal-container">
                    <div id="thermal-image">
                      <img
                        id="temp-image"
                        src="/temp.jpg"
                        alt="Ảnh nhiệt"
                        style="width: 100%; height: 100%; object-fit: cover"
                      />
                    </div>
                    <div class="thermal-overlay">
                      <div>Nhiệt độ tối đa: <span id="max-temp">0°C</span></div>
                      <div>
                        Nhiệt độ tối thiểu: <span id="min-temp">0°C</span>
                      </div>
                      <div>
                        Nhiệt độ trung bình: <span id="avg-temp">0°C</span>
                      </div>
                    </div>
                    <div class="thermal-stats">
                      <span>Min: <span id="min-temp-display">--</span>°C</span>
                      <span>Max: <span id="max-temp-display">--</span>°C</span>
                      <span>Avg: <span id="avg-temp-display">--</span>°C</span>
                    </div>
                    <div
                      id="last-update-display"
                      style="
                        text-align: center;
                        margin-top: 10px;
                        color: #666;
                        font-size: 14px;
                      "
                    >
                      Chưa có dữ liệu
                    </div>
                    <div class="thermal-controls">
                      <div class="row">
                        <div class="col-6">
                          <label for="colorScheme" class="form-label"
                            >Bảng Màu:</label
                          >
                          <select class="form-select" id="colorScheme">
                            <option value="1">Nhiệt (Đỏ-Vàng-Trắng)</option>
                            <option value="2">Cầu Vồng</option>
                            <option value="3">Sắc Lạnh (Xanh-Tím-Đỏ)</option>
                          </select>
                        </div>
                        <div class="col-6">
                          <label for="refreshRate" class="form-label"
                            >Tốc Độ Làm Mới:</label
                          >
                          <select class="form-select" id="refreshRate">
                            <option value="3000">3 giây</option>
                            <option value="5000" selected>5 giây</option>
                            <option value="10000">10 giây</option>
                            <option value="15000">15 giây</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="thermal-legend mt-3">
                    <div class="legend-item">
                      <div
                        class="legend-color"
                        style="background-color: blue"
                      ></div>
                      <span>Lạnh</span>
                    </div>
                    <div class="legend-item">
                      <div
                        class="legend-color"
                        style="background-color: green"
                      ></div>
                      <span>Mát</span>
                    </div>
                    <div class="legend-item">
                      <div
                        class="legend-color"
                        style="background-color: yellow"
                      ></div>
                      <span>Ấm</span>
                    </div>
                    <div class="legend-item">
                      <div
                        class="legend-color"
                        style="background-color: red"
                      ></div>
                      <span>Nóng</span>
                    </div>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="stats-card">
                    <div class="stats-title">Nhiệt Độ Tối Đa</div>
                    <div class="stats-value" id="max-temp-value">0°C</div>
                  </div>

                  <div class="stats-card">
                    <div class="stats-title">Nhiệt Độ Tối Thiểu</div>
                    <div class="stats-value" id="min-temp-value">0°C</div>
                  </div>

                  <div class="stats-card">
                    <div class="stats-title">Nhiệt Độ Trung Bình</div>
                    <div class="stats-value" id="avg-temp-value">0°C</div>
                  </div>

                  <div class="stats-card">
                    <div class="stats-title">Điểm Nóng Phát Hiện</div>
                    <div class="stats-value" id="hotspots-value">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Temperature Chart -->
      <div class="row">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header">
              <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                  <a
                    class="nav-link active"
                    id="realtime-tab"
                    data-bs-toggle="tab"
                    href="#realtime"
                    >Thời Gian Thực</a
                  >
                </li>
                <li class="nav-item">
                  <a
                    class="nav-link"
                    id="history-tab"
                    data-bs-toggle="tab"
                    href="#history"
                    >Lịch Sử</a
                  >
                </li>
                <li class="nav-item">
                  <a
                    class="nav-link"
                    id="analysis-tab"
                    data-bs-toggle="tab"
                    href="#analysis"
                    >Phân Tích</a
                  >
                </li>
              </ul>
            </div>
            <div class="card-body">
              <div class="tab-content">
                <div class="tab-pane fade show active" id="realtime">
                  <h5 class="card-title">Biểu Đồ Nhiệt Độ Thời Gian Thực</h5>
                  <p class="text-muted">
                    Theo dõi sự thay đổi nhiệt độ theo thời gian thực.
                  </p>
                  <div class="temperature-chart-container">
                    <canvas id="realtimeChart"></canvas>
                  </div>
                </div>
                <div class="tab-pane fade" id="history">
                  <h5 class="card-title">Lịch Sử Nhiệt Độ</h5>
                  <p class="text-muted">
                    Xem lại dữ liệu nhiệt độ trong 24 giờ qua.
                  </p>
                  <div class="temperature-chart-container">
                    <canvas id="historyChart"></canvas>
                  </div>
                </div>
                <div class="tab-pane fade" id="analysis">
                  <h5 class="card-title">Phân Tích Nhiệt Độ</h5>
                  <p class="text-muted">
                    Phân tích chi tiết về phân bố nhiệt độ.
                  </p>
                  <div class="temperature-chart-container">
                    <canvas id="analysisChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="row">
          <div class="col-4">
            <h3>ESP32 Monitor</h3>
            <p>
              Giải pháp giám sát môi trường tiên tiến cho nhà ở và doanh nghiệp.
            </p>
          </div>
          <div class="col-4">
            <h3>Liên Kết Nhanh</h3>
            <ul>
              <li><a href="/home">Bảng Điều Khiển</a></li>
              <li><a href="/devices">Thiết Bị</a></li>
              <li><a href="/mlx90640">Ảnh Tầm Nhiệt</a></li>
            </ul>
          </div>
          <div class="col-4">
            <h3>Liên Hệ</h3>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +84 123 456 789</p>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-linkedin"></i></a>
            </div>
          </div>
        </div>
        <div class="text-center mt-4">
          <p>&copy; 2023 ESP32 Monitor. Đã đăng ký bản quyền.</p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Biến toàn cục
      let isAdmin = false;
      let currentUser = "";
      let currentDevice = "";
      let thermalData = [];
      let thermalHistory = [];
      let heatmapInstance = null;
      let refreshInterval = 5000; // mặc định 5 giây
      let colorScheme = 1; // mặc định là nhiệt
      let realtimeChart = null;
      let historyChart = null;
      let analysisChart = null;

      // Cập nhật thời gian
      function updateTimestamp() {
        document.getElementById("lastUpdate").textContent =
          new Date().toLocaleTimeString();
      }

      // Biến để theo dõi trạng thái tải ảnh
      let isImageLoading = false;
      let pendingImageRefresh = false;

      // Khởi tạo hiển thị ảnh nhiệt
      function initThermalImage() {
        console.log("Khởi tạo hiển thị ảnh nhiệt");

        // Tạo một đối tượng Image mới để tải ảnh trước khi hiển thị
        const newImage = new Image();
        const timestamp = new Date().getTime();

        newImage.onload = function () {
          // Khi ảnh đã tải xong, cập nhật src của thẻ img hiện tại
          const tempImage = document.getElementById("temp-image");
          if (tempImage) {
            tempImage.src = newImage.src;
          }
        };

        newImage.onerror = function () {
          console.error("Không thể tải ảnh nhiệt ban đầu");
        };

        // Bắt đầu tải ảnh từ database
        newImage.src = `/thermal_image.jpg?t=${timestamp}`;
      }

      // Cập nhật heatmap với dữ liệu mới
      function updateHeatmap(data) {
        if (!heatmapInstance) return;

        // Nếu không có dữ liệu, hiển thị heatmap trống
        if (!data || data.length === 0) {
          heatmapInstance.setData({
            max: 50,
            min: 0,
            data: [],
          });
          return;
        }

        // Chuyển đổi mảng 2D thành dữ liệu heatmap
        const width = data[0].length || 32; // Giả sử 32 cột
        const height = data.length || 24; // Giả sử 24 hàng

        const containerWidth =
          document.getElementById("thermal-image").offsetWidth;
        const containerHeight =
          document.getElementById("thermal-image").offsetHeight;

        const cellWidth = containerWidth / width;
        const cellHeight = containerHeight / height;

        // Tìm giá trị min/max
        let min = 100;
        let max = 0;
        let sum = 0;
        let count = 0;

        // Dữ liệu cho heatmap
        const heatmapData = [];

        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const value = data[y][x];

            if (value < min) min = value;
            if (value > max) max = value;
            sum += value;
            count++;

            heatmapData.push({
              x: Math.floor(x * cellWidth + cellWidth / 2),
              y: Math.floor(y * cellHeight + cellHeight / 2),
              value: value,
            });
          }
        }

        // Tính nhiệt độ trung bình
        const avg = sum / count;

        // Cập nhật thông tin nhiệt độ
        document.getElementById("max-temp").textContent = `${max.toFixed(1)}°C`;
        document.getElementById("min-temp").textContent = `${min.toFixed(1)}°C`;
        document.getElementById("avg-temp").textContent = `${avg.toFixed(1)}°C`;

        document.getElementById("max-temp-value").textContent = `${max.toFixed(
          1
        )}°C`;
        document.getElementById("min-temp-value").textContent = `${min.toFixed(
          1
        )}°C`;
        document.getElementById("avg-temp-value").textContent = `${avg.toFixed(
          1
        )}°C`;

        // Đếm số điểm nóng (nhiệt độ > avg + 5)
        const hotspots = heatmapData.filter(
          (point) => point.value > avg + 5
        ).length;
        document.getElementById("hotspots-value").textContent = hotspots;

        // Áp dụng bảng màu
        let gradient = {};

        switch (colorScheme) {
          case "1": // Nhiệt (Đỏ-Vàng-Trắng)
            gradient = {
              0.0: "blue",
              0.25: "green",
              0.5: "yellow",
              0.75: "orange",
              1.0: "red",
            };
            break;
          case "2": // Cầu vồng
            gradient = {
              0.0: "blue",
              0.2: "cyan",
              0.4: "lime",
              0.6: "yellow",
              0.8: "orange",
              1.0: "red",
            };
            break;
          case "3": // Sắc lạnh (Xanh-Tím-Đỏ)
            gradient = {
              0.0: "rgba(0, 0, 255, 1)",
              0.5: "rgba(128, 0, 255, 1)",
              1.0: "rgba(255, 0, 0, 1)",
            };
            break;
          default:
            gradient = {
              0.0: "blue",
              0.25: "green",
              0.5: "yellow",
              0.75: "orange",
              1.0: "red",
            };
        }

        // Cập nhật heatmap
        heatmapInstance.setData({
          max: max,
          min: min,
          data: heatmapData,
        });

        heatmapInstance.configure({
          gradient: gradient,
        });

        // Cập nhật biểu đồ thời gian thực
        updateRealtimeChart(max, min, avg);
      }

      // Tải dữ liệu MLX90640
      function loadThermalData() {
        // Cập nhật thời gian
        updateTimestamp();

        // Chỉ tải ảnh mới nếu không có quá trình tải đang diễn ra
        if (!isImageLoading) {
          isImageLoading = true;

          // Tạo một đối tượng Image mới để tải ảnh trước khi hiển thị
          const newImage = new Image();
          const timestamp = new Date().getTime();
          newImage.onload = function () {
            // Khi ảnh đã tải xong, cập nhật src của thẻ img hiện tại
            const tempImage = document.getElementById("temp-image");
            if (tempImage) {
              tempImage.src = newImage.src;
            }
            isImageLoading = false;

            // Nếu có yêu cầu làm mới đang chờ, thực hiện nó
            if (pendingImageRefresh) {
              pendingImageRefresh = false;
              setTimeout(loadThermalData, 100);
            }
          };

          newImage.onerror = function () {
            console.error("Không thể tải ảnh nhiệt");
            isImageLoading = false;

            // Nếu có yêu cầu làm mới đang chờ, thực hiện nó
            if (pendingImageRefresh) {
              pendingImageRefresh = false;
              setTimeout(loadThermalData, 1000); // Thử lại sau 1 giây nếu có lỗi
            }
          };

          // Bắt đầu tải ảnh mới từ database
          newImage.src = `/thermal_image.jpg?t=${timestamp}`;
        } else {
          // Đánh dấu rằng có yêu cầu làm mới đang chờ
          pendingImageRefresh = true;
        }

        // Tải dữ liệu nhiệt độ để cập nhật các giá trị hiển thị
        fetch(`/get_thermal_data`, {
          credentials: "include",
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error("Không thể tải dữ liệu tầm nhiệt");
            }
            return response.json();
          })
          .then((data) => {
            if (data.status === "success") {
              // Cập nhật thông tin nhiệt độ
              if (data.max_temp) {
                document.getElementById(
                  "max-temp"
                ).textContent = `${data.max_temp.toFixed(1)}°C`;
                document.getElementById(
                  "max-temp-value"
                ).textContent = `${data.max_temp.toFixed(1)}°C`;
              }
              if (data.min_temp) {
                document.getElementById(
                  "min-temp"
                ).textContent = `${data.min_temp.toFixed(1)}°C`;
                document.getElementById(
                  "min-temp-value"
                ).textContent = `${data.min_temp.toFixed(1)}°C`;
              }
              if (data.avg_temp) {
                document.getElementById(
                  "avg-temp"
                ).textContent = `${data.avg_temp.toFixed(1)}°C`;
                document.getElementById(
                  "avg-temp-value"
                ).textContent = `${data.avg_temp.toFixed(1)}°C`;
              }

              // Lưu dữ liệu cho biểu đồ
              thermalData = data.data;

              // Cập nhật biểu đồ thời gian thực
              updateRealtimeChart(data.max_temp, data.min_temp, data.avg_temp);
            } else {
              console.error("Lỗi:", data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi khi tải dữ liệu tầm nhiệt:", error);
          });
      }

      // Tải lịch sử dữ liệu MLX90640
      function loadThermalHistory() {
        fetch(`/get_mlx90640_history`, {
          credentials: "include",
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error("Không thể tải lịch sử dữ liệu tầm nhiệt");
            }
            return response.json();
          })
          .then((data) => {
            if (data.status === "success") {
              thermalHistory = data.history;
              updateHistoryChart();
              updateAnalysisChart();
            } else {
              console.error("Lỗi:", data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi khi tải lịch sử dữ liệu tầm nhiệt:", error);
          });
      }

      // Khởi tạo biểu đồ thời gian thực
      function initCharts() {
        // Biểu đồ thời gian thực
        const realtimeCtx = document
          .getElementById("realtimeChart")
          .getContext("2d");
        realtimeChart = new Chart(realtimeCtx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Nhiệt độ tối đa",
                data: [],
                borderColor: "rgba(255, 99, 132, 1)",
                backgroundColor: "rgba(255, 99, 132, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
              {
                label: "Nhiệt độ tối thiểu",
                data: [],
                borderColor: "rgba(54, 162, 235, 1)",
                backgroundColor: "rgba(54, 162, 235, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
              {
                label: "Nhiệt độ trung bình",
                data: [],
                borderColor: "rgba(75, 192, 192, 1)",
                backgroundColor: "rgba(75, 192, 192, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: false,
                title: {
                  display: true,
                  text: "Nhiệt độ (°C)",
                },
              },
              x: {
                title: {
                  display: true,
                  text: "Thời gian",
                },
              },
            },
          },
        });

        // Biểu đồ lịch sử
        const historyCtx = document
          .getElementById("historyChart")
          .getContext("2d");
        historyChart = new Chart(historyCtx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Nhiệt độ tối đa",
                data: [],
                borderColor: "rgba(255, 99, 132, 1)",
                backgroundColor: "rgba(255, 99, 132, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
              {
                label: "Nhiệt độ tối thiểu",
                data: [],
                borderColor: "rgba(54, 162, 235, 1)",
                backgroundColor: "rgba(54, 162, 235, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
              {
                label: "Nhiệt độ trung bình",
                data: [],
                borderColor: "rgba(75, 192, 192, 1)",
                backgroundColor: "rgba(75, 192, 192, 0.2)",
                borderWidth: 2,
                tension: 0.4,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: false,
                title: {
                  display: true,
                  text: "Nhiệt độ (°C)",
                },
              },
              x: {
                title: {
                  display: true,
                  text: "Thời gian",
                },
              },
            },
          },
        });

        // Biểu đồ phân tích
        const analysisCtx = document
          .getElementById("analysisChart")
          .getContext("2d");
        analysisChart = new Chart(analysisCtx, {
          type: "bar",
          data: {
            labels: [
              "0-10°C",
              "10-20°C",
              "20-30°C",
              "30-40°C",
              "40-50°C",
              "50+°C",
            ],
            datasets: [
              {
                label: "Phân bố nhiệt độ",
                data: [0, 0, 0, 0, 0, 0],
                backgroundColor: [
                  "rgba(54, 162, 235, 0.5)",
                  "rgba(75, 192, 192, 0.5)",
                  "rgba(255, 206, 86, 0.5)",
                  "rgba(255, 159, 64, 0.5)",
                  "rgba(255, 99, 132, 0.5)",
                  "rgba(153, 102, 255, 0.5)",
                ],
                borderColor: [
                  "rgba(54, 162, 235, 1)",
                  "rgba(75, 192, 192, 1)",
                  "rgba(255, 206, 86, 1)",
                  "rgba(255, 159, 64, 1)",
                  "rgba(255, 99, 132, 1)",
                  "rgba(153, 102, 255, 1)",
                ],
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: "Số lượng điểm ảnh",
                },
              },
              x: {
                title: {
                  display: true,
                  text: "Khoảng nhiệt độ",
                },
              },
            },
          },
        });
      }

      // Cập nhật biểu đồ thời gian thực
      function updateRealtimeChart(max, min, avg) {
        if (!realtimeChart) return;

        const now = new Date().toLocaleTimeString();

        // Thêm dữ liệu mới
        realtimeChart.data.labels.push(now);
        realtimeChart.data.datasets[0].data.push(max);
        realtimeChart.data.datasets[1].data.push(min);
        realtimeChart.data.datasets[2].data.push(avg);

        // Giới hạn số điểm dữ liệu hiển thị
        if (realtimeChart.data.labels.length > 20) {
          realtimeChart.data.labels.shift();
          realtimeChart.data.datasets.forEach((dataset) => {
            dataset.data.shift();
          });
        }

        realtimeChart.update();
      }

      // Cập nhật biểu đồ lịch sử
      function updateHistoryChart() {
        if (!historyChart || !thermalHistory || thermalHistory.length === 0)
          return;

        // Xóa dữ liệu cũ
        historyChart.data.labels = [];
        historyChart.data.datasets[0].data = [];
        historyChart.data.datasets[1].data = [];
        historyChart.data.datasets[2].data = [];

        // Thêm dữ liệu mới
        thermalHistory.forEach((item) => {
          const time = new Date(item.timestamp * 1000).toLocaleTimeString();
          historyChart.data.labels.push(time);
          historyChart.data.datasets[0].data.push(item.max);
          historyChart.data.datasets[1].data.push(item.min);
          historyChart.data.datasets[2].data.push(item.avg);
        });

        historyChart.update();
      }

      // Cập nhật biểu đồ phân tích
      function updateAnalysisChart() {
        if (!analysisChart || !thermalData || thermalData.length === 0) return;

        // Tính toán phân bố nhiệt độ
        const distribution = [0, 0, 0, 0, 0, 0]; // 0-10, 10-20, 20-30, 30-40, 40-50, 50+

        // Làm phẳng mảng 2D
        const flatData = [];
        for (let row of thermalData) {
          flatData.push(...row);
        }

        // Đếm số lượng điểm ảnh trong mỗi khoảng nhiệt độ
        for (let temp of flatData) {
          if (temp < 10) distribution[0]++;
          else if (temp < 20) distribution[1]++;
          else if (temp < 30) distribution[2]++;
          else if (temp < 40) distribution[3]++;
          else if (temp < 50) distribution[4]++;
          else distribution[5]++;
        }

        // Cập nhật biểu đồ
        analysisChart.data.datasets[0].data = distribution;
        analysisChart.update();
      }

      // Chụp ảnh tầm nhiệt
      function captureImage() {
        const container = document.querySelector(".thermal-container");

        // Sử dụng html2canvas nếu có
        if (window.html2canvas) {
          html2canvas(container).then((canvas) => {
            const image = canvas.toDataURL("image/png");
            const link = document.createElement("a");
            link.href = image;
            link.download = `thermal_image_${new Date().toISOString()}.png`;
            link.click();
          });
        } else {
          alert(
            "Chức năng chụp ảnh không khả dụng. Vui lòng thêm thư viện html2canvas."
          );
        }
      }

      // Đăng xuất
      function logout() {
        fetch("/logout", {
          method: "POST",
          credentials: "include",
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.status === "success") {
              window.location.href = "/";
            } else {
              alert("Đăng xuất thất bại: " + data.message);
            }
          })
          .catch((error) => {
            console.error("Lỗi:", error);
            alert("Đăng xuất thất bại. Vui lòng thử lại.");
          });
      }

      // Biến để theo dõi trạng thái chuyển hướng
      let isRedirecting = false;

      // Xử lý sự kiện khi trang tải xong
      window.onload = function () {
        // Ẩn nội dung chính ban đầu
        document.getElementById("mainContent").style.display = "none";

        // Hiển thị navbar cho khách trước khi kiểm tra phiên
        document.getElementById("guestNavbar").style.display = "flex";
        document.getElementById("userNavbar").style.display = "none";

        // Kiểm tra xem URL hiện tại có phải là trang đăng nhập không
        const currentPath = window.location.pathname;
        if (currentPath === "/login") {
          // Nếu đã ở trang đăng nhập, không cần chuyển hướng nữa
          return;
        }

        // Kiểm tra phiên đăng nhập
        fetch("/check_session", {
          credentials: "include",
        })
          .then((response) => {
            if (response.ok) {
              return response.json();
            } else {
              console.log("Chưa đăng nhập, hiển thị navbar cho khách");
              // Người dùng chưa đăng nhập - không chuyển hướng ngay lập tức
              // để tránh vòng lặp vô hạn

              // Chỉ chuyển hướng nếu chưa trong quá trình chuyển hướng
              if (!isRedirecting) {
                isRedirecting = true;
                console.log("Chuyển hướng đến trang đăng nhập sau 3 giây");
                setTimeout(function () {
                  window.location.href = "/login";
                }, 3000);
              }

              return null;
            }
          })
          .then((data) => {
            if (data && data.status === "success") {
              console.log("Đã đăng nhập, hiển thị nội dung");
              // Người dùng đã đăng nhập
              document.getElementById("guestNavbar").style.display = "none";
              document.getElementById("userNavbar").style.display = "flex";
              document.getElementById("mainContent").style.display = "block";

              document.getElementById("currentUser").textContent =
                data.username;
              currentUser = data.username;
              isAdmin = data.isAdmin;

              // Khởi tạo hiển thị ảnh nhiệt
              initThermalImage();

              // Khởi tạo biểu đồ
              initCharts();

              // Tải dữ liệu tầm nhiệt
              loadThermalData();

              // Tải lịch sử dữ liệu tầm nhiệt
              loadThermalHistory();

              // Thiết lập cập nhật định kỳ - tăng lên 5 giây để giảm tải
              refreshInterval = 5000;
              window.thermalInterval = setInterval(
                loadThermalData,
                refreshInterval
              );
              window.historyInterval = setInterval(
                loadThermalHistory,
                refreshInterval * 2
              );
            } else if (data === null) {
              // Đã xử lý trong phần response.ok == false
            } else {
              console.log("Lỗi phiên, hiển thị giao diện khách");
              // Người dùng chưa đăng nhập hoặc phiên không hợp lệ

              // Chỉ chuyển hướng nếu chưa trong quá trình chuyển hướng
              if (!isRedirecting) {
                isRedirecting = true;
                console.log("Chuyển hướng đến trang đăng nhập sau 3 giây");
                setTimeout(function () {
                  window.location.href = "/login";
                }, 3000);
              }
            }
          })
          .catch((error) => {
            console.error("Lỗi kiểm tra phiên:", error);

            // Chỉ chuyển hướng nếu chưa trong quá trình chuyển hướng
            if (!isRedirecting) {
              isRedirecting = true;
              console.log("Chuyển hướng đến trang đăng nhập sau 3 giây");
              setTimeout(function () {
                window.location.href = "/login";
              }, 3000);
            }
          });

        // Xử lý sự kiện thay đổi bảng màu
        document
          .getElementById("colorScheme")
          .addEventListener("change", function () {
            colorScheme = this.value;
            if (thermalData.length > 0) {
              updateHeatmap(thermalData);
            }
          });

        // Xử lý sự kiện thay đổi tốc độ làm mới
        document
          .getElementById("refreshRate")
          .addEventListener("change", function () {
            refreshInterval = parseInt(this.value);
            // Xóa các interval cũ và thiết lập lại
            clearInterval(window.thermalInterval);
            clearInterval(window.historyInterval);
            window.thermalInterval = setInterval(
              loadThermalData,
              refreshInterval
            );
            window.historyInterval = setInterval(
              loadThermalHistory,
              refreshInterval * 2
            );
          });

        // Xử lý sự kiện nút chụp ảnh
        document
          .getElementById("captureBtn")
          .addEventListener("click", captureImage);
      };

      // WebSocket connection for real-time updates
      const socket = io();

      socket.on("connect", function () {
        console.log("Đã kết nối WebSocket");
        showNotification("Đã kết nối WebSocket", "success");
      });

      socket.on("disconnect", function () {
        console.log("Mất kết nối WebSocket");
        showNotification("Mất kết nối WebSocket", "error");
      });

      socket.on("thermal_image_updated", function (data) {
        console.log("Ảnh nhiệt mới:", data);

        // Cập nhật ảnh từ S3 URL
        const imgElement = document.getElementById("temp-image");
        if (imgElement && data.url) {
          imgElement.src = data.url + "?t=" + Date.now(); // Cache busting
          imgElement.onload = function () {
            console.log("Ảnh đã tải thành công từ S3");
          };
          imgElement.onerror = function () {
            console.log("Lỗi tải ảnh từ S3");
            showNotification("Lỗi tải ảnh từ S3", "error");
          };
        }

        // Cập nhật thông tin nhiệt độ
        if (data.stats) {
          // Cập nhật overlay
          document.getElementById("min-temp").textContent =
            data.stats.min_temp.toFixed(1) + "°C";
          document.getElementById("max-temp").textContent =
            data.stats.max_temp.toFixed(1) + "°C";
          document.getElementById("avg-temp").textContent =
            data.stats.avg_temp.toFixed(1) + "°C";

          // Cập nhật thermal-stats
          document.getElementById("min-temp-display").textContent =
            data.stats.min_temp.toFixed(1);
          document.getElementById("max-temp-display").textContent =
            data.stats.max_temp.toFixed(1);
          document.getElementById("avg-temp-display").textContent =
            data.stats.avg_temp.toFixed(1);

          // Cập nhật stats cards
          document.getElementById("min-temp-value").textContent =
            data.stats.min_temp.toFixed(1) + "°C";
          document.getElementById("max-temp-value").textContent =
            data.stats.max_temp.toFixed(1) + "°C";
          document.getElementById("avg-temp-value").textContent =
            data.stats.avg_temp.toFixed(1) + "°C";
        }

        // Cập nhật thời gian
        const now = new Date();
        document.getElementById(
          "last-update-display"
        ).textContent = `Cập nhật lúc: ${now.toLocaleTimeString()}`;
        document.getElementById("lastUpdate").textContent =
          now.toLocaleTimeString();

        // Hiển thị thông báo
        showNotification("Ảnh nhiệt mới đã được cập nhật!", "success");

        // Cập nhật biểu đồ real-time
        if (data.stats) {
          updateRealtimeChart(
            data.stats.max_temp,
            data.stats.min_temp,
            data.stats.avg_temp
          );
        }
      });

      function showNotification(message, type = "success") {
        // Xóa notification cũ nếu có
        const oldNotification = document.querySelector(".notification");
        if (oldNotification) {
          oldNotification.remove();
        }

        // Tạo notification mới
        const notification = document.createElement("div");
        notification.className = "notification";
        notification.style.background =
          type === "success" ? "#4CAF50" : "#f44336";
        notification.textContent = message;
        document.body.appendChild(notification);

        // Tự động xóa sau 3 giây
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 3000);
      }

      // Tự động refresh ảnh mỗi 30 giây (fallback)
      setInterval(function () {
        const imgElement = document.getElementById("temp-image");
        if (imgElement && imgElement.src.includes("/temp.jpg")) {
          // Chỉ refresh nếu đang hiển thị ảnh local, không phải S3
          imgElement.src = imgElement.src.split("?")[0] + "?t=" + Date.now();
        }
      }, 30000);
    </script>
  </body>
</html>
